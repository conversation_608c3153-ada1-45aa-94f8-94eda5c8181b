package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"bonusearned/config"
	"bonusearned/domain/merchant/service"
	"bonusearned/infra/database"
	"bonusearned/infra/logger"
	"bonusearned/infra/persistence"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("local")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	db, err := database.NewPostgresDB(cfg.Postgres)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化Redis
	redisClient, err := database.NewRedisClient(cfg.Redis)
	if err != nil {
		log.Fatalf("Failed to connect to redis: %v", err)
	}

	// 初始化日志
	zapLogger, err := logger.NewLogger(cfg.Logger)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	// 创建仓储和服务
	countryRepo := persistence.NewCountryPostgresRepository(db)
	countryService := service.NewCountryService(countryRepo, zapLogger, redisClient)

	// 创建Gin上下文
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	req, _ := http.NewRequest("GET", "/", nil)
	ctx.Request = req.WithContext(context.Background())

	// 测试缓存功能
	fmt.Println("=== 测试CountryService缓存功能 ===")

	// 先查询所有国家，找一个存在的ID
	fmt.Println("\n0. 先查询所有国家列表，找一个存在的ID")
	allCountries, total, err := countryService.GetCountryListByCondition(ctx, map[string]interface{}{"status": 1})
	if err != nil {
		fmt.Printf("Error getting country list: %v\n", err)
		return
	}
	fmt.Printf("Query successful: found %d countries (total: %d)\n", len(allCountries), total)
	if len(allCountries) == 0 {
		fmt.Println("No countries found in database")
		return
	}

	testCountryID := allCountries[0].ID
	fmt.Printf("Found %d countries, using ID=%d for testing\n", len(allCountries), testCountryID)

	// 1. 测试GetCountryDetailById - 第一次查询（从数据库）
	fmt.Printf("\n1. 第一次查询国家ID=%d（从数据库）\n", testCountryID)
	start := time.Now()
	country1, err := countryService.GetCountryDetailById(ctx, testCountryID)
	duration1 := time.Since(start)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else if country1 != nil {
		fmt.Printf("Country: ID=%d, Name=%s, Code=%s\n", country1.ID, country1.Name, country1.Code)
		fmt.Printf("查询耗时: %v\n", duration1)
	} else {
		fmt.Println("Country not found")
	}

	// 2. 测试GetCountryDetailById - 第二次查询（从本地缓存）
	fmt.Printf("\n2. 第二次查询国家ID=%d（从本地缓存）\n", testCountryID)
	start = time.Now()
	country2, err := countryService.GetCountryDetailById(ctx, testCountryID)
	duration2 := time.Since(start)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else if country2 != nil {
		fmt.Printf("Country: ID=%d, Name=%s, Code=%s\n", country2.ID, country2.Name, country2.Code)
		fmt.Printf("查询耗时: %v\n", duration2)
		if duration2 > 0 {
			fmt.Printf("缓存加速比: %.2fx\n", float64(duration1)/float64(duration2))
		}
	} else {
		fmt.Println("Country not found")
	}

	// 3. 测试GetCountryDetailByCode
	fmt.Println("\n3. 查询国家代码US（从数据库）")
	start = time.Now()
	countryUS1, err := countryService.GetCountryDetailByCode(ctx, "US")
	duration3 := time.Since(start)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Country: %+v\n", countryUS1)
		fmt.Printf("查询耗时: %v\n", duration3)
	}

	// 4. 再次查询相同代码（从本地缓存）
	fmt.Println("\n4. 再次查询国家代码US（从本地缓存）")
	start = time.Now()
	countryUS2, err := countryService.GetCountryDetailByCode(ctx, "US")
	duration4 := time.Since(start)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Country: %+v\n", countryUS2)
		fmt.Printf("查询耗时: %v\n", duration4)
		fmt.Printf("缓存加速比: %.2fx\n", float64(duration3)/float64(duration4))
	}

	// 5. 测试GetCountryListByCondition
	fmt.Println("\n5. 查询国家列表（从数据库）")
	condition := map[string]interface{}{"status": 1}
	start = time.Now()
	countries1, total1, err := countryService.GetCountryListByCondition(ctx, condition)
	duration5 := time.Since(start)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Countries count: %d, Total: %d\n", len(countries1), total1)
		fmt.Printf("查询耗时: %v\n", duration5)
	}

	// 6. 再次查询相同条件（从本地缓存）
	fmt.Println("\n6. 再次查询国家列表（从本地缓存）")
	start = time.Now()
	countries2, total2, err := countryService.GetCountryListByCondition(ctx, condition)
	duration6 := time.Since(start)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Countries count: %d, Total: %d\n", len(countries2), total2)
		fmt.Printf("查询耗时: %v\n", duration6)
		fmt.Printf("缓存加速比: %.2fx\n", float64(duration5)/float64(duration6))
	}

	fmt.Println("\n=== 缓存测试完成 ===")
}
