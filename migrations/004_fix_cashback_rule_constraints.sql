-- 修复 cashback_rules 表的约束问题
-- 1. 删除旧的唯一索引
DROP INDEX IF EXISTS idx_user_merchant;

-- 2. 修改 merchant_id 字段，允许为 0（全局规则）
ALTER TABLE cashback_rules ALTER COLUMN merchant_id SET DEFAULT 0;

-- 3. 创建新的索引
-- 用户全局规则索引（每个用户只能有一个全局规则）
CREATE UNIQUE INDEX idx_user_global ON cashback_rules (user_id, is_global) 
WHERE is_global = true AND status = 1;

-- 用户特定商家规则索引（每个用户每个商家只能有一个规则）
CREATE UNIQUE INDEX idx_user_merchant_specific ON cashback_rules (user_id, merchant_id) 
WHERE is_global = false AND status = 1 AND merchant_id > 0;

-- 4. 更新现有的全局规则数据，确保 merchant_id 为 0
UPDATE cashback_rules 
SET merchant_id = 0 
WHERE is_global = true;

-- 5. 添加检查约束
ALTER TABLE cashback_rules 
ADD CONSTRAINT chk_global_merchant_id 
CHECK (
    (is_global = true AND merchant_id = 0) OR 
    (is_global = false AND merchant_id > 0)
);

-- 6. 添加时间范围检查约束
ALTER TABLE cashback_rules 
ADD CONSTRAINT chk_time_range 
CHECK (
    (start_time IS NULL AND end_time IS NULL) OR
    (start_time IS NOT NULL AND end_time IS NOT NULL AND start_time < end_time) OR
    (start_time IS NULL AND end_time IS NOT NULL) OR
    (start_time IS NOT NULL AND end_time IS NULL)
);

-- 7. 添加返现比例范围检查约束
ALTER TABLE cashback_rules 
ADD CONSTRAINT chk_cashback_rate_range 
CHECK (cashback_rate >= 0 AND cashback_rate <= 1);
