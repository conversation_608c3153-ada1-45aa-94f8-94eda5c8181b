import React, { useEffect, useRef } from 'react';

interface HelmetProps {
  children?: React.ReactNode;
}

/**
 * Modern Helmet component compatible with React 19
 * 
 * This component provides a modern alternative to react-helmet that doesn't use
 * deprecated lifecycle methods. It safely manages document head elements using
 * useEffect and proper cleanup.
 */
const Helmet: React.FC<HelmetProps> = ({ children }) => {
  const elementsRef = useRef<Element[]>([]);

  useEffect(() => {
    if (!children) return;

    // Create a temporary container to parse the JSX children
    const tempContainer = document.createElement('div');
    
    // Convert React children to HTML string and parse
    const processChildren = (child: React.ReactNode): void => {
      if (!child) return;

      if (typeof child === 'string') {
        // Handle text content (shouldn't happen in head, but just in case)
        return;
      }

      if (React.isValidElement(child)) {
        const element = child as React.ReactElement;
        
        // Create the actual DOM element
        const domElement = document.createElement(element.type as string);
        
        // Set attributes
        if (element.props) {
          Object.entries(element.props).forEach(([key, value]) => {
            if (key === 'children') {
              if (typeof value === 'string') {
                domElement.textContent = value;
              }
            } else if (key === 'dangerouslySetInnerHTML') {
              if (value && typeof value === 'object' && '__html' in value) {
                domElement.innerHTML = (value as { __html: string }).__html;
              }
            } else if (typeof value === 'string' || typeof value === 'number') {
              // Convert React prop names to HTML attribute names
              const attrName = key === 'className' ? 'class' : 
                              key === 'htmlFor' ? 'for' : 
                              key.replace(/([A-Z])/g, '-$1').toLowerCase();
              domElement.setAttribute(attrName, String(value));
            }
          });
        }

        // Add to document head
        document.head.appendChild(domElement);
        elementsRef.current.push(domElement);
      }

      if (Array.isArray(child)) {
        child.forEach(processChildren);
      }
    };

    // Process all children
    React.Children.forEach(children, processChildren);

    // Cleanup function
    return () => {
      elementsRef.current.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
      elementsRef.current = [];
    };
  }, [children]);

  // This component doesn't render anything visible
  return null;
};

export default Helmet;
export { Helmet };
