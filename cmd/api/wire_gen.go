// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"bonusearned/application/blog/appservice"
	appservice2 "bonusearned/application/clickrecord/appservice"
	appservice4 "bonusearned/application/coupon/appservice"
	appservice3 "bonusearned/application/merchant/appservice"
	appservice5 "bonusearned/application/order/appservice"
	appservice7 "bonusearned/application/user/appservice"
	appservice6 "bonusearned/application/withdrawal/appservice"
	"bonusearned/config"
	"bonusearned/domain/blog/service"
	service3 "bonusearned/domain/clickrecord/service"
	service4 "bonusearned/domain/coupon/service"
	service2 "bonusearned/domain/merchant/service"
	service5 "bonusearned/domain/order/service"
	service7 "bonusearned/domain/user/service"
	service6 "bonusearned/domain/withdrawal/service"
	"bonusearned/infra/database"
	"bonusearned/infra/logger"
	"bonusearned/infra/persistence"
	"bonusearned/interfaces/api/handler/blog"
	"bonusearned/interfaces/api/handler/clickrecord"
	"bonusearned/interfaces/api/handler/coupon"
	"bonusearned/interfaces/api/handler/merchant"
	"bonusearned/interfaces/api/handler/order"
	"bonusearned/interfaces/api/handler/seo"
	"bonusearned/interfaces/api/handler/user"
	"bonusearned/interfaces/api/handler/withdrawal"
	"bonusearned/interfaces/api/router"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Injectors from wire.go:

// InitializeApplication 初始化应用程序
func InitializeApplication(cfg *config.Config) (*Application, error) {
	db, err := ProvidePostgres(cfg)
	if err != nil {
		return nil, err
	}
	blogRepository := persistence.NewBlogPostgresRepository(db)
	logger, err := ProvideLogger(cfg)
	if err != nil {
		return nil, err
	}
	blogService := service.NewBlogService(blogRepository, logger)
	merchantRepository := persistence.NewMerchantPostgresRepository(db)
	cashbackRuleRepository := persistence.NewCashbackRulePostgresRepository(db)
	client, err := ProvideRedis(cfg)
	if err != nil {
		return nil, err
	}
	cashbackRuleService := service2.NewCashbackRuleService(cashbackRuleRepository, logger, client)
	merchantService := service2.NewMerchantService(merchantRepository, cashbackRuleService, client, cfg, logger)
	blogAppService := appservice.NewBlogAppService(blogService, merchantService)
	blogHandler := blog.NewBlogHandler(blogAppService, logger)
	clickRepository := persistence.NewClickPostgresRepository(db)
	clickRecordService := service3.NewClickRecordService(clickRepository, logger)
	clickRecordAppService := appservice2.NewClickRecordAppService(clickRecordService, merchantService)
	clickRecordHandler := clickrecord.NewClickHandler(clickRecordAppService)
	categoryRepository := persistence.NewCategoryPostgresRepository(db)
	categoryService := service2.NewCategoryService(categoryRepository, logger, client)
	categoryAppService := appservice3.NewCategoryAppService(categoryService)
	categoryHandler := merchant.NewCategoryHandler(categoryAppService)
	countryRepository := persistence.NewCountryPostgresRepository(db)
	countryService := service2.NewCountryService(countryRepository, logger, client)
	countryHandler := merchant.NewCountryHandler(countryService)
	couponRepository := persistence.NewCouponPostgresRepository(db)
	couponService := service4.NewCouponService(couponRepository)
	couponAppService := appservice4.NewCouponAppService(couponService, merchantService)
	couponHandler := coupon.NewCouponHandler(couponAppService)
	merchantAppService := appservice3.NewMerchantAppService(merchantService, categoryService)
	handler := merchant.NewHandler(merchantAppService)
	orderRepository := persistence.NewOrderPostgresRepository(db)
	orderService := service5.NewOrderService(orderRepository, client, logger)
	orderAppService := appservice5.NewOrderAppService(orderService, merchantService, logger)
	orderHandler := order.NewOrderHandler(orderAppService, logger)
	withdrawalRepository := persistence.NewWithdrawalPostgresRepository(db)
	withdrawalService := service6.NewWithdrawalService(withdrawalRepository)
	userRepository := persistence.NewUserPostgresRepository(db)
	userService := service7.NewUserService(userRepository, client, cfg, logger)
	withdrawalAppService := appservice6.NewWithdrawalApp(withdrawalService, userService)
	withdrawalHandler := withdrawal.NewWithdrawalHandler(withdrawalAppService)
	userApp := appservice7.NewUserApp(userService, logger)
	userHandler := user.NewUserHandler(userApp, logger)
	seoHandler := seo.NewSEOHandler(merchantAppService, blogAppService, logger)
	apiServer := router.NewApiServer(blogHandler, clickRecordHandler, categoryHandler, countryHandler, couponHandler, handler, orderHandler, cfg, client, withdrawalHandler, userHandler, seoHandler)
	application := &Application{
		Router:     apiServer,
		Logger:     logger,
		Config:     cfg,
		PostgresDB: db,
		RedisDB:    client,
	}
	return application, nil
}

// wire.go:

// ProviderSet 定义所有依赖注入的提供者
var ProviderSet = wire.NewSet(

	ProvidePostgres,
	ProvideRedis,
	ProvideLogger, persistence.NewBlogPostgresRepository, persistence.NewClickPostgresRepository, persistence.NewMerchantPostgresRepository, persistence.NewOrderPostgresRepository, persistence.NewCouponPostgresRepository, persistence.NewWithdrawalPostgresRepository, persistence.NewUserPostgresRepository, persistence.NewCategoryPostgresRepository, persistence.NewCountryPostgresRepository, persistence.NewCashbackRulePostgresRepository, service.NewBlogService, service3.NewClickRecordService, service2.NewMerchantService, service5.NewOrderService, service4.NewCouponService, service6.NewWithdrawalService, service7.NewUserService, service2.NewCategoryService, service2.NewCountryService, service2.NewCashbackRuleService, appservice.NewBlogAppService, appservice2.NewClickRecordAppService, appservice3.NewMerchantAppService, appservice5.NewOrderAppService, appservice4.NewCouponAppService, appservice6.NewWithdrawalApp, appservice3.NewCategoryAppService, appservice3.NewCashbackRuleAppService, appservice7.NewUserApp, blog.NewBlogHandler, clickrecord.NewClickHandler, merchant.NewCategoryHandler, merchant.NewCountryHandler, coupon.NewCouponHandler, merchant.NewHandler, order.NewOrderHandler, withdrawal.NewWithdrawalHandler, user.NewUserHandler, seo.NewSEOHandler, router.NewApiServer,
)

// Application 应用程序结构体
type Application struct {
	Router     *router.ApiServer
	Logger     *zap.Logger
	Config     *config.Config
	PostgresDB *gorm.DB
	RedisDB    *redis.Client
}

// ProvidePostgres 提供 PostgreSQL 连接
func ProvidePostgres(cfg *config.Config) (*gorm.DB, error) {
	return database.NewPostgresDB(cfg.Postgres)
}

// ProvideRedis 提供 Redis 连接
func ProvideRedis(cfg *config.Config) (*redis.Client, error) {
	return database.NewRedisClient(cfg.Redis)
}

// ProvideLogger 提供日志实例
func ProvideLogger(cfg *config.Config) (*zap.Logger, error) {
	return logger.NewLogger(cfg.Logger)
}
