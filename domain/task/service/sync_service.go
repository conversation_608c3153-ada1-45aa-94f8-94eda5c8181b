package service

import (
	"bonusearned/config"
	clickrecordentity "bonusearned/domain/clickrecord/entity"
	clickrecordservice "bonusearned/domain/clickrecord/service"
	couponentity "bonusearned/domain/coupon/entity"
	couponservice "bonusearned/domain/coupon/service"
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	merchantservice "bonusearned/domain/merchant/service"
	orderentity "bonusearned/domain/order/entity"
	orderservice "bonusearned/domain/order/service"
	userentity "bonusearned/domain/user/entity"
	userservice "bonusearned/domain/user/service"
	"bonusearned/infra/constant"
	"bonusearned/infra/database/postgres"
	"bonusearned/infra/ecode"
	"bonusearned/infra/external_gateway/exchangeratelib"
	"bonusearned/infra/external_gateway/imgboxlib"
	"bonusearned/infra/networklib/blueafflib"
	"bonusearned/infra/networklib/joingekkolib"
	"bonusearned/infra/networklib/linkbuxlib"
	"bonusearned/infra/networklib/partnerboostlib"
	"bonusearned/infra/utils/listutils"
	"bonusearned/infra/utils/trackurlutil"
	"bonusearned/infra/utils/uniqueutil"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

// SyncService 同步服务接口
type SyncService interface {
	// SyncMerchantList 同步商家信息
	SyncMerchantList(ctx *gin.Context)
	SyncCouponList(ctx *gin.Context)
	SyncOrderList(ctx *gin.Context)
	// SyncMerchantLogoList 同步商家Logo
	SyncMerchantLogoList(ctx *gin.Context)
}

type syncService struct {
	merchantService merchantservice.MerchantService
	countryService  merchantservice.CountryService

	merchantRepo repository.MerchantRepository

	orderService       orderservice.OrderService
	clickRecordService clickrecordservice.ClickRecordService
	couponService      couponservice.CouponService
	userService        userservice.UserService
	config             *config.Config
}

// NewSyncService 创建同步服务
func NewSyncService(merchantService merchantservice.MerchantService, countryService merchantservice.CountryService, merchantRepo repository.MerchantRepository, orderService orderservice.OrderService, clickRecordService clickrecordservice.ClickRecordService, couponService couponservice.CouponService, userService userservice.UserService, config *config.Config) SyncService {
	return &syncService{
		merchantService:    merchantService,
		countryService:     countryService,
		merchantRepo:       merchantRepo,
		orderService:       orderService,
		clickRecordService: clickRecordService,
		couponService:      couponService,
		userService:        userService,
		config:             config,
	}
}

func (s *syncService) SyncMerchantList(ctx *gin.Context) {
	fmt.Println("开始同步商家信息")
	// 1. 循环账号列表
	for _, platformMap := range constant.MerchantsAccountList {
		//accountName := platformMap["account_name"].(string)
		platformType := platformMap["type"].(string)

		// 2. 从数据库中获取对应商家信息
		existingMerchants, _, err := s.merchantRepo.GetMerchantListByConditionWithAllFields(ctx, map[string]interface{}{"platform_type": platformType})
		if err != nil {
			continue
		}

		// 创建商家ID映射，用于快速查找
		merchantMap := make(map[string]*entity.Merchant)
		merchantCodeMap := make(map[string]bool)
		for _, m := range existingMerchants {
			merchantMap[m.PlatformMerchantID] = m
			merchantCodeMap[m.MerchantCode] = true
		}

		// 3. 获取平台商家数据并转换为entity
		var merchants []*entity.Merchant
		if platformType == constant.AccountTypeBlueAff {
			token := platformMap["token"].(string)
			clickIdKey := platformMap["click_id"].(string)
			sub1Key := platformMap["sub1"].(string)

			// 获取商家数据
			merchantsData, err := blueafflib.BatchGetMerchants(token)
			if err != nil {
				fmt.Println("blueafflib.BatchGetMerchants:: ", err)
				continue
			}
			fmt.Println("获取到商家:", len(merchantsData))
			merchants = s.toEntityCreateMerchant(ctx, merchantsData, merchantCodeMap, clickIdKey, sub1Key, platformType)
			fmt.Println("转换商家：", len(merchants))
		}
		if platformType == constant.AccountTypeJoinGekko {
			publisherKey := platformMap["publisherKey"].(string)
			propertyID := platformMap["propertyID"].(string)
			authKey := platformMap["authKey"].(string)
			secretKey := platformMap["secretKey"].(string)
			clickIdKey := platformMap["click_id"].(string)
			sub1Key := platformMap["sub1"].(string)

			// 获取商家数据
			merchantsData, err := joingekkolib.BatchGetMerchants(secretKey, publisherKey, propertyID, authKey)
			if err != nil {
				fmt.Println("joingekkolib.BatchGetMerchants:: ", err)
				continue
			}
			fmt.Println(":获取到商家:", len(merchantsData))
			merchants = s.toEntityCreateMerchant(ctx, merchantsData, merchantCodeMap, clickIdKey, sub1Key, platformType)
			fmt.Println("转换商家：", len(merchants))
		}
		if platformType == constant.AccountTypePb {
			token := platformMap["token"].(string)
			limit := platformMap["limit"].(int)
			accountName := platformMap["account_name"].(string)
			clickIdKey := platformMap["click_id"].(string)
			sub1Key := platformMap["sub1"].(string)
			// 获取商家数据
			merchantsData, err := partnerboostlib.BatchGetMerchants(token, limit, "Joined", accountName)
			if err != nil {
				fmt.Println("partnerboostlib.BatchGetMerchants: ", err)
				continue
			}
			merchants = s.toEntityCreateMerchant(ctx, merchantsData, merchantCodeMap, clickIdKey, sub1Key, platformType)
		}
		// 4. 创建或更新商家信息
		var createMerchants []*entity.Merchant
		var updateMerchants []*entity.Merchant

		uniqueMerchantMap := make(map[string]bool)
		for _, merchant := range merchants {
			if _, exists := uniqueMerchantMap[merchant.UniqueName]; exists {
				continue
			}
			// 5. 根据PlatformType和PlatformMerchantID判断商家是否存在
			if existingMerchant, exists := merchantMap[merchant.PlatformMerchantID]; exists {
				// 更新现有商家信息
				merchant.ID = existingMerchant.ID
				updateMerchants = append(updateMerchants, merchant)
			} else {
				// 创建新商家
				createMerchants = append(createMerchants, merchant)
			}
			uniqueMerchantMap[merchant.UniqueName] = true
		}

		// 批量创建新商家
		fmt.Println("创建商家：", len(createMerchants))
		if len(createMerchants) > 0 {
			if err := s.merchantService.BatchCreateMerchants(ctx, createMerchants); err != nil {
				fmt.Println("创建商家失败：", err)
				zap.L().Error("批量创建商家失败",
					zap.String("platform_type", platformType),
					zap.Error(err))
			} else {
				// 同步成功后更新国家商家数量
				s.updateCountryMerchantCounts(ctx)
			}
		}

		// 批量更新现有商家
		fmt.Println("更新商家：", len(updateMerchants))
		if len(updateMerchants) > 0 {
			if err := s.merchantService.BatchUpdateMerchants(ctx, updateMerchants); err != nil {
				zap.L().Error("批量更新商家失败",
					zap.String("platform_type", platformType),
					zap.Error(err))
			}
		}
	}
	fmt.Println("同步商家信息完成")

	return
}

func (s *syncService) SyncOrderList(ctx *gin.Context) {
	fmt.Println("开始同步订单信息")

	// 获取汇率
	getExchangeRates, err := exchangeratelib.GetExchangeRates()
	if err != nil {
		zap.L().Error("获取汇率失败", zap.Error(err))
		return
	}

	// 存储转换成美元的汇率
	exchangeRatesUsdMap := make(map[string]float64, len(getExchangeRates.Rates)+1)
	getExchangeRates.Rates["TWD"] = 33
	exchangeRatesUsdMap[getExchangeRates.Base] = getExchangeRates.Rates["USD"]
	for currency_id, exchangeRateValue := range getExchangeRates.Rates {
		exchangeRatesUsdMap[currency_id] = getExchangeRates.Rates["USD"] / exchangeRateValue
	}

	// 并发获取所需数据
	var (
		clickRecordMap = make(map[string]*clickrecordentity.ClickRecord)
		merchantMap    = make(map[string]*entity.Merchant)
		userMap        = make(map[string]*userentity.User)
		errChan        = make(chan error, 3)
	)

	// 并发获取点击记录、商家信息和用户信息
	var wg sync.WaitGroup
	wg.Add(3)

	go func() {
		defer wg.Done()
		clickRecordList, _, err := s.clickRecordService.GetClickRecordListByCondition(ctx, map[string]interface{}{})
		if err != nil {
			errChan <- fmt.Errorf("获取点击记录失败: %w", err)
			return
		}
		for _, clickRecord := range clickRecordList {
			clickRecordMap[clickRecord.ClickID] = clickRecord
		}
	}()

	go func() {
		defer wg.Done()
		merchantList, _, err := s.merchantRepo.GetMerchantListByConditionWithAllFields(ctx, map[string]interface{}{})
		if err != nil {
			errChan <- fmt.Errorf("获取商家列表失败: %w", err)
			return
		}
		for _, merchant := range merchantList {
			merchantMap[merchant.MerchantCode] = merchant
		}
	}()

	go func() {
		defer wg.Done()
		userList, _, err := s.userService.GetUserListByCondition(ctx, map[string]interface{}{})
		if err != nil {
			errChan <- fmt.Errorf("获取用户列表失败: %w", err)
			return
		}
		for _, user := range userList {
			userMap[user.UserCode] = user
		}
	}()

	// 等待所有数据加载完成
	wg.Wait()
	close(errChan)

	// 检查是否有错误发生
	for err := range errChan {
		if err != nil {
			zap.L().Error("数据加载失败", zap.Error(err))
			return
		}
	}

	// 创建结果通道
	type platformResult struct {
		platformType string
		createOrders []*orderentity.Order
		updateOrders []*orderentity.Order
		err          error
	}
	resultChan := make(chan platformResult)

	// 并发处理不同平台的订单
	activePlatforms := 0
	for _, platformMap := range constant.OrdersAccountList {
		activePlatforms++
		go func(platform map[string]interface{}) {
			result := platformResult{
				platformType: platform["type"].(string),
			}

			// 获取现有订单
			existingOrders, _, err := s.orderService.GetOrderListByCondition(ctx, map[string]interface{}{"platform_type": result.platformType})
			if err != nil {
				result.err = fmt.Errorf("获取现有订单失败: %w", err)
				resultChan <- result
				return
			}

			// 创建订单映射
			orderMap := make(map[string]*orderentity.Order, len(existingOrders))
			for _, order := range existingOrders {
				orderMap[order.PlatformConversionId] = order
			}
			// 处理订单数据
			orderList := s.processUpstreamOrders(ctx, platform, exchangeRatesUsdMap, clickRecordMap, merchantMap, userMap)
			// 分类订单（新建/更新）
			for _, order := range orderList {
				if existingOrder, exists := orderMap[order.PlatformConversionId]; exists {
					order.ID = existingOrder.ID
					order.ConversionId = existingOrder.ConversionId
					result.updateOrders = append(result.updateOrders, order)
				} else {
					result.createOrders = append(result.createOrders, order)
				}
			}

			resultChan <- result
		}(platformMap)
	}

	// 收集并处理结果
	for i := 0; i < activePlatforms; i++ {
		result := <-resultChan
		if result.err != nil {
			zap.L().Error("处理平台订单失败",
				zap.String("platform_type", result.platformType),
				zap.Error(result.err))
			continue
		}

		// 批量创建新订单
		if len(result.createOrders) > 0 {
			if err := s.orderService.BatchCreateOrder(ctx, result.createOrders); err != nil {
				zap.L().Error("批量创建订单失败",
					zap.String("platform_type", result.platformType),
					zap.Error(err))
			} else {
				zap.L().Info("批量创建订单成功",
					zap.String("platform_type", result.platformType),
					zap.Int("count", len(result.createOrders)))
			}
		}

		// 批量更新现有订单
		if len(result.updateOrders) > 0 {
			if err := s.orderService.BatchUpdateOrder(ctx, result.updateOrders); err != nil {
				zap.L().Error("批量更新订单失败",
					zap.String("platform_type", result.platformType),
					zap.Error(err))
			} else {
				zap.L().Info("批量更新订单成功",
					zap.String("platform_type", result.platformType),
					zap.Int("count", len(result.updateOrders)))
			}
		}
	}

	// 同步用户余额信息
	fmt.Println("开始同步用户余额信息")
	if err := s.syncUserBalances(ctx); err != nil {
		zap.L().Error("同步用户余额失败", zap.Error(err))
	} else {
		fmt.Println("同步用户余额信息完成")
	}

	fmt.Println("同步订单信息完成")
}

// processUpstreamOrders 处理上游平台的订单
func (s *syncService) processUpstreamOrders(ctx *gin.Context, platformMap map[string]interface{}, exchangeRatesUsdMap map[string]float64, clickRecordMap map[string]*clickrecordentity.ClickRecord, merchantMap map[string]*entity.Merchant, userMap map[string]*userentity.User) []*orderentity.Order {
	var orderList []*orderentity.Order
	platformType := platformMap["type"].(string)
	accountName := platformMap["account_name"].(string)

	// 获取平台特定的配置
	var getOrders func(startDay, endDay int) ([]map[string]interface{}, *ecode.Error)
	switch platformType {
	case constant.AccountTypeBlueAff:
		//tokenEf := platformMap["token_ef"].(string)
		tokenPublish := platformMap["token"].(string)
		limit := platformMap["limit"].(int)
		getOrders = func(startDay, endDay int) ([]map[string]interface{}, *ecode.Error) {
			return blueafflib.BatchGetOrderTransactions(accountName, tokenPublish, "", "all", 1, limit, startDay, endDay, exchangeRatesUsdMap)
		}
	case constant.AccountTypeJoinGekko:
		// JoinGekko 平台的订单获取实现
		// TODO: 实现 JoinGekko 平台的订单获取逻辑
		getOrders = func(startDay, endDay int) ([]map[string]interface{}, *ecode.Error) {
			return nil, nil
		}
	// 可以添加其他平台的处理逻辑
	case constant.AccountTypePb:
		// pb 平台的订单获取实现
		token := platformMap["token"].(string)
		limit := platformMap["limit"].(int)
		getOrders = func(startDay, endDay int) ([]map[string]interface{}, *ecode.Error) {
			return partnerboostlib.BatchGetOrderTransactions(accountName, token, "all", 1, limit, startDay, endDay)
		}
	// 可以添加其他平台的处理逻辑
	default:
		zap.L().Error("不支持的平台类型",
			zap.String("platform_type", platformType),
			zap.String("account_name", accountName))
		return orderList
	}

	// 获取平台的时间配置
	timeConfig := s.getPlatformTimeConfig(platformType)

	// 按时间段获取订单
	for i := 1; i < timeConfig.periodCount; i++ {
		startDay := timeConfig.startDayOffset * i
		endDay := timeConfig.endDayOffset + (timeConfig.startDayOffset * (i - 1))

		// 获取订单数据
		ordersData, err := getOrders(startDay, endDay)
		if err != nil {
			zap.L().Error("获取订单数据失败",
				zap.String("platform_type", platformType),
				zap.String("account_name", accountName),
				zap.Int("start_day", startDay),
				zap.Int("end_day", endDay),
				zap.Error(err))
			continue
		}
		fmt.Println("获取订单数据成功:", platformType, accountName)
		// 处理订单数据
		newOrders := s.toEntityCreateOrder(ordersData, platformType, clickRecordMap, merchantMap, userMap)
		orderList = append(orderList, newOrders...)
	}

	return orderList
}

// platformTimeConfig 平台时间配置
type platformTimeConfig struct {
	periodCount    int // 时间段数量
	startDayOffset int // 开始日期偏移量
	endDayOffset   int // 结束日期偏移量
}

// getPlatformTimeConfig 获取平台的时间配置
func (s *syncService) getPlatformTimeConfig(platformType string) platformTimeConfig {
	switch platformType {
	case constant.AccountTypeBlueAff:
		return platformTimeConfig{
			periodCount:    2,
			startDayOffset: -25,
			endDayOffset:   2,
		}
	default:
		return platformTimeConfig{
			periodCount:    2,
			startDayOffset: -25,
			endDayOffset:   2,
		}
	}
}

func (s *syncService) SyncCouponList(ctx *gin.Context) {
	zap.L().Info("开始同步 coupon 信息")
	existingCoupons, _, err := s.couponService.GetCouponListByCondition(ctx, map[string]interface{}{})
	if err != nil {
		return
	}
	existingMerchants, _, err := s.merchantService.GetMerchantListByCondition(ctx, map[string]interface{}{}, 0, "")
	if err != nil {
		return
	}
	merchantLinkMap := make(map[string]*entity.Merchant)
	for _, m := range existingMerchants {
		merchantLinkMap[m.Website] = m
	}
	// 创建coupon code映射，用于快速查找
	couponCodeMap := make(map[string]*couponentity.Coupon)
	for _, c := range existingCoupons {
		couponCodeMap[c.Code] = c
	}

	for _, platformMap := range constant.CouponsAccountList {
		platformType := platformMap["type"].(string)
		var coupons []*couponentity.Coupon
		if platformType == constant.AccountTypeLinkBux {
			token := platformMap["token"].(string)
			limit := platformMap["limit"].(int)
			// 获取优惠券数据
			couponsData, err := linkbuxlib.BatchGetCoupons(token, limit)
			if err != nil {
				fmt.Println("linkbuxlib.BatchGetCoupons:: ", err)
				continue
			}
			coupons = s.toEntityCreateCoupon(couponsData, merchantLinkMap)
		}
		// 4. 创建或更新商家信息
		var createCoupons []*couponentity.Coupon
		uniqueCouponMap := make(map[string]bool)
		for _, coupon := range coupons {
			if _, exists := uniqueCouponMap[coupon.Code]; exists {
				continue
			}
			if _, exists := couponCodeMap[coupon.Code]; !exists {
				createCoupons = append(createCoupons, coupon)
			}
			uniqueCouponMap[coupon.Code] = true
		}

		// 批量创建新商家
		if len(createCoupons) > 0 {
			if err := s.couponService.BatchCreateCoupon(ctx, createCoupons); err != nil {
				fmt.Println("创建coupon失败：", err)
				zap.L().Error("批量创建coupon失败",
					zap.String("platform_type", platformType),
					zap.Error(err))
			}
		}
	}
	fmt.Println("同步coupon信息完成")
	return
}

func (s *syncService) toEntityCreateMerchant(ctx *gin.Context, createDataRows []map[string]interface{}, merchantCodeMap map[string]bool, clickIdKey, sub1Key string, platformType string) (merchantList []*entity.Merchant) {
	merchantList = []*entity.Merchant{}
	for _, data := range createDataRows {
		merchant := &entity.Merchant{}
		merchant.Name = data["name"].(string)
		merchant.UniqueName = strings.ReplaceAll(data["unique_name"].(string), "/", "")
		// 生成商家编码并检查重复
		var code string
		for i := 0; i < 3; i++ { // 最多尝试3次
			code = uniqueutil.GenerateCode(merchant.UniqueName)
			if !merchantCodeMap[code] {
				break
			}
		}
		// 如果3次都重复，先忽略此商家，等待下一次同步
		if merchantCodeMap[code] {
			continue
		}

		merchant.MerchantCode = code
		merchantCodeMap[code] = true
		merchant.Website = data["domain"].(string)
		merchant.OriginalDomain = data["original_domain"].(string)

		logoURL := data["logo"].(string)
		merchant.Logo = logoURL
		merchant.TrackURL = trackurlutil.GenBaseTrackURL(s.config.Track.Service.TrackHost, merchant.MerchantCode)
		merchant.AffiliateLink = data["affiliate_link"].(string)

		merchant.CashbackRate = decimal.NewFromFloat(0.85)

		merchant.CashbackType = data["cashback_type"].(string)
		merchant.CashbackValue = decimal.NewFromFloat(data["cashback_value"].(float64)).Mul(merchant.CashbackRate)
		merchant.CashbackIsUpto = data["cashback_is_upto"].(bool)
		merchant.CashbackIsRevShare = data["cashback_is_rev_share"].(bool)

		merchant.AlternativeCashbackType = data["alternative_cashback_type"].(string)
		merchant.AlternativeCashbackValue = decimal.NewFromFloat(data["alternative_cashback_value"].(float64)).Mul(merchant.CashbackRate)
		merchant.AlternativeCashbackIsUpto = data["alternative_cashback_is_upto"].(bool)
		merchant.AlternativeCashbackIsRevShare = data["alternative_cashback_is_rev_share"].(bool)

		merchant.ParentCashbackValue = data["parent_cashback_value"].(string)

		merchant.ParamMappings = entity.JSONMap{"click_id": clickIdKey, "sub1": sub1Key}
		merchant.Description = data["description"].(string)
		merchant.Status = constant.MerchantStatusEnabled
		merchant.PlatformType = platformType
		merchant.PlatformMerchantID = data["id"].(string)

		category := data["category"].(string)
		categoryId := uint64(71)
		if _, ok := constant.CategoryNameMap[category]; ok {
			categoryId = constant.CategoryNameMap[category]
		}
		merchant.CategoryID = categoryId // 根据名字查找对应id
		merchant.Featured = data["featured"].(bool)

		// 处理国家信息
		countryValue := data["country"].(string)
		// 根据国家代码或名称获取国家ID，如果找不到则跳过该商家
		countryID, shouldSync := s.getCountryIDByCodeOrName(ctx, countryValue)
		if !shouldSync {
			// 国家不在支持列表中，跳过该商家
			continue
		}
		merchant.CountryID = countryID

		// 处理支持的国家列表
		supportedCountries := strings.TrimSpace(data["supported_countries"].(string))
		var countries []string
		if strings.Contains(supportedCountries, ",") {
			countries = strings.Split(supportedCountries, ",")
		} else if supportedCountries != "" {
			countries = []string{supportedCountries}
		}
		merchant.SupportedCountries = entity.StringArray(countries)

		merchant.CreatedAt = time.Now()
		merchant.UpdatedAt = time.Now()
		merchantList = append(merchantList, merchant)
	}

	return
}

func (s *syncService) toEntityCreateCoupon(createDataRows []map[string]interface{}, merchantLinkMap map[string]*entity.Merchant) (couponList []*couponentity.Coupon) {
	couponList = []*couponentity.Coupon{}
	for _, data := range createDataRows {
		coupon := &couponentity.Coupon{}
		if merchant, ok := merchantLinkMap[data["domain"].(string)]; ok {
			coupon.MerchantID = merchant.ID
		} else {
			continue
		}

		if len(data["coupon_code"].(string)) <= 0 || data["coupon_code"].(string) == "No Coupons Needed" {
			continue
		}

		coupon.Code = data["coupon_code"].(string)
		coupon.Title = data["coupon_title"].(string)
		coupon.Description = data["description"].(string)
		coupon.CouponType = "coupon"
		coupon.DiscountRate = data["discount"].(string)
		coupon.PlatformType = data["platform_type"].(string)
		startedAtDate, err := time.Parse("2006-01-02", data["started_at"].(string))
		if err != nil {
			fmt.Println("日期解析错误:", err, data["started_at"].(string))
			continue
		}
		endedAtDate, err := time.Parse("2006-01-02", data["ended_at"].(string))
		if err != nil {
			fmt.Println("日期解析错误:", err, data["ended_at"].(string))
			continue
		}
		coupon.StartedAt = &startedAtDate
		coupon.EndedAt = &endedAtDate

		coupon.CreatedAt = time.Now()
		coupon.UpdatedAt = time.Now()
		couponList = append(couponList, coupon)
	}
	return
}

func (s *syncService) toEntityCreateOrder(createDataRows []map[string]interface{}, platformType string, clickRecordMap map[string]*clickrecordentity.ClickRecord, merchantMap map[string]*entity.Merchant, userMap map[string]*userentity.User) (orderList []*orderentity.Order) {
	orderList = []*orderentity.Order{}

	for _, data := range createDataRows {
		order := &orderentity.Order{}
		order.ClickID = data["click_id"].(string)
		// 先通过 clickid 查找点击记录
		if clickRecord, ok := clickRecordMap[order.ClickID]; ok {
			// 找到了点击记录，直接使用记录中的信息
			order.UserID = clickRecord.UserID
			order.UserCode = clickRecord.UserCode
			order.MerchantID = clickRecord.MerchantID
			order.MerchantCode = clickRecord.MerchantCode
			order.Sub1 = clickRecord.Sub1
		} else {
			// 没找到点击记录，尝试解码点击ID
			merchantCode, userCode, sub1, err := uniqueutil.DecodeClick(order.ClickID, constant.ClickKey)
			if err != nil {
				zap.L().Error("解码点击ID失败",
					zap.String("click_id", order.ClickID),
					zap.Error(err))
				continue
			}

			// 设置用户编码和sub1参数
			order.UserCode = userCode
			order.Sub1 = sub1

			// 查找商家信息
			if merchant, ok := merchantMap[merchantCode]; ok {
				order.MerchantCode = merchantCode
				order.MerchantID = merchant.ID
			} else {
				continue
			}

			// 从 userMap 中获取用户信息
			if user, ok := userMap[userCode]; ok {
				order.UserID = user.ID
			} else {
				continue
			}
		}

		// 设置订单基本信息
		order.OrderID = data["order_id"].(string)
		order.OrderAmount = decimal.NewFromFloat(data["amount"].(float64))
		commissionUsd := decimal.NewFromFloat(data["commission_usd"].(float64))

		if merchant, ok := merchantMap[order.MerchantCode]; ok {
			// 使用商家的实际返现比例，而不是硬编码的80%
			order.CashbackAmount = commissionUsd.Mul(merchant.CashbackRate)
		} else {
			// 如果找不到商家信息，使用默认的80%返现比例
			order.CashbackAmount = commissionUsd.Mul(decimal.NewFromFloat(0.8))
		}
		platFormStatus := data["order_status"].(string)
		order.PlatformStatus = platFormStatus
		order.Status = orderentity.OrderStatusPending
		if listutils.IsInList(platFormStatus, orderentity.PlatformOrderRejected) {
			order.Status = orderentity.OrderStatusRejected
		} else if listutils.IsInList(platFormStatus, orderentity.PlatformOrderPaid) {
			order.Status = orderentity.OrderStatusApproved
		}

		order.PlatformType = platformType
		order.PlatformMerchantID = data["merchant_id"].(string)
		order.PlatformConversionId = data["id"].(string)

		// 生成转化ID
		conversionId, _ := uniqueutil.GenerateConversionID()
		order.ConversionId = conversionId

		// 设置订单时间
		if orderTimeStr, ok := data["order_time_sec"].(string); ok && orderTimeStr != "" {
			// 解析订单时间，支持多种格式
			var orderTime time.Time
			var err error

			// 尝试解析不同的时间格式
			timeFormats := []string{
				"2006-1-2 15:04:05",
				"2006-01-02 15:04:05",
				"2006-1-2",
				"2006-01-02",
			}

			for _, format := range timeFormats {
				orderTime, err = time.Parse(format, orderTimeStr)
				if err == nil {
					break
				}
			}

			if err != nil {
				zap.L().Error("解析订单时间失败",
					zap.String("order_time_str", orderTimeStr),
					zap.String("click_id", order.ClickID),
					zap.Error(err))
				// 如果解析失败，使用当前时间
				orderTime = time.Now()
			}

			order.OrderTime = orderTime
		} else {
			// 如果没有订单时间数据，使用当前时间
			order.OrderTime = time.Now()
		}

		// 设置时间相关字段
		order.LastSyncTime = time.Now()
		order.CreatedAt = time.Now()
		order.UpdatedAt = time.Now()

		orderList = append(orderList, order)
	}

	return
}

// SyncMerchantLogoList 同步商家Logo
func (s *syncService) SyncMerchantLogoList(ctx *gin.Context) {
	fmt.Println("开始同步商家Logo")
	// 1. 获取所有商家
	merchants, _, err := s.merchantRepo.GetMerchantListByConditionWithAllFields(ctx, map[string]interface{}{})
	if err != nil {
		zap.L().Error("获取商家列表失败", zap.Error(err))
		return
	}

	// 从配置中获取上传令牌
	uploadToken := s.config.ImgBox.UploadToken

	// 2. 顺序处理每个商家的Logo
	updateMerchantMap := make(map[uint64]*entity.Merchant, 0) // 存储商家ID和对应的新Logo
	for _, merchant := range merchants {
		// 跳过空Logo或已经是imgbox链接的Logo
		if merchant.Logo == "" || strings.Contains(merchant.Logo, s.config.ImgBox.PublicURL) {
			fmt.Println("跳过", merchant.Logo)
			continue
		}

		fmt.Println("处理商家Logo:", merchant.Name, merchant.Logo)

		// 使用imgboxlib直接从URL上传图片
		uploadResp, err := imgboxlib.UploadImageFromURL(uploadToken, merchant.Logo, "true", merchant.Website)
		if err != nil {
			// 如果上传失败，使用默认图片
			filename := constant.DefaultImageName
			fmt.Println("使用默认图片:", filename)
			merchant.Logo = fmt.Sprintf("%s%s", s.config.ImgBox.PublicURL, filename)
		} else {
			// 获取上传后的文件名
			filename := uploadResp.Data.ImgUrl
			if filename == "" {
				filename = constant.DefaultImageName
			}
			merchant.Logo = fmt.Sprintf("%s%s", s.config.ImgBox.PublicURL, filename)
		}
		fmt.Println("更新后的Logo:", merchant.Logo)
		updateMerchantMap[merchant.ID] = merchant
	}
	updateMerchantList := make([]*entity.Merchant, 0)
	for _, merchant := range updateMerchantMap {
		updateMerchantList = append(updateMerchantList, merchant)
	}
	// 处理剩余的商家Logo
	if len(updateMerchantList) > 0 {
		if err := s.merchantRepo.BatchUpdateMerchants(ctx, updateMerchantList); err != nil {
			zap.L().Error("批量更新商家Logo失败", zap.Error(err))
		}
	}

	fmt.Println("同步商家Logo完成，处理", len(updateMerchantList), "个商家Logo")
}

// syncUserBalances 同步用户余额信息
func (s *syncService) syncUserBalances(ctx *gin.Context) *ecode.Error {
	// 获取所有用户
	userList, _, err := s.userService.GetUserListByCondition(ctx, map[string]interface{}{})
	if err != nil {
		return err
	}

	if len(userList) == 0 {
		return nil
	}

	// 计算每个用户的余额
	userBalanceMap := make(map[uint64]*userentity.UserBalance)

	// 为每个用户初始化余额
	for _, user := range userList {
		userBalanceMap[user.ID] = &userentity.UserBalance{
			TotalAmount:    0,
			PendingAmount:  0,
			PaidAmount:     0,
			ApprovedAmount: 0,
		}
	}

	// 获取所有订单并按用户ID分组计算余额
	allOrders, _, err := s.orderService.GetOrderListByCondition(ctx, map[string]interface{}{})
	if err != nil {
		return err
	}

	// 计算每个用户的余额
	for _, order := range allOrders {
		if balance, exists := userBalanceMap[order.UserID]; exists {
			cashbackFloat, _ := order.CashbackAmount.Float64()

			switch order.Status {
			case orderentity.OrderStatusPending:
				balance.PendingAmount += cashbackFloat
			case orderentity.OrderStatusApproved:
				balance.ApprovedAmount += cashbackFloat
			case orderentity.OrderStatusPaid:
				balance.PaidAmount += cashbackFloat
			}

			// 计算总金额（不包括已取消的订单）
			if order.Status != orderentity.OrderStatusRejected {
				balance.TotalAmount += cashbackFloat
			}
		}
	}

	// 准备批量更新的用户列表
	var usersToUpdate []*userentity.User
	for _, user := range userList {
		if balance, exists := userBalanceMap[user.ID]; exists {
			// 将余额转换为JSON格式
			balanceMap := map[string]interface{}{
				"total_amount":    balance.TotalAmount,
				"pending_amount":  balance.PendingAmount,
				"paid_amount":     balance.PaidAmount,
				"approved_amount": balance.ApprovedAmount,
			}
			balanceJSON, err := postgres.FromMap(balanceMap)
			if err != nil {
				zap.L().Error("转换用户余额为JSON失败",
					zap.Uint64("user_id", user.ID),
					zap.Error(err))
				continue
			}

			// 创建用户副本用于更新
			userCopy := *user
			userCopy.UserBalance = balanceJSON
			usersToUpdate = append(usersToUpdate, &userCopy)
		}
	}

	// 批量更新用户余额
	if len(usersToUpdate) > 0 {
		if err := s.userService.BatchUpdateUserBalance(ctx, usersToUpdate); err != nil {
			return err
		}
		zap.L().Info("批量更新用户余额成功", zap.Int("count", len(usersToUpdate)))
	}

	return nil
}

// getCountryIDByCodeOrName 根据国家代码或名称获取国家ID
// 返回值：(countryID, shouldSync)
// shouldSync为false表示该国家不在支持列表中，应跳过同步
func (s *syncService) getCountryIDByCodeOrName(ctx *gin.Context, countryValue string) (uint64, bool) {
	if countryValue == "" {
		// 空值不同步
		return 0, false
	}

	// 首先检查是否为有效的国家代码
	if constant.IsValidCountryCode(countryValue) {
		country, err := s.countryService.GetCountryDetailByCode(ctx, countryValue)
		if err == nil {
			return country.ID, true
		}
	}

	// 如果不是有效的国家代码，检查是否为有效的国家名称
	if constant.IsValidCountryName(countryValue) {
		// 根据国家名称获取国家代码
		if countryCode, exists := constant.GetCountryCodeByName(countryValue); exists {
			country, err := s.countryService.GetCountryDetailByCode(ctx, countryCode)
			if err == nil {
				return country.ID, true
			}
		}
	}

	// 如果都找不到，不进行同步
	return 0, false
}

// updateCountryMerchantCounts 更新所有国家的商家数量
func (s *syncService) updateCountryMerchantCounts(ctx *gin.Context) {
	// 获取所有国家
	countries, _, err := s.countryService.GetCountryListByCondition(ctx, map[string]interface{}{})
	if err != nil {
		zap.L().Error("获取国家列表失败", zap.Error(err))
		return
	}

	// 更新每个国家的商家数量
	for _, country := range countries {
		// 统计该国家的商家数量
		_, count, err := s.merchantRepo.GetMerchantListByConditionWithAllFields(ctx, map[string]interface{}{
			"country_id": country.ID,
		})
		if err != nil {
			zap.L().Error("统计国家商家数量失败",
				zap.Uint64("country_id", country.ID),
				zap.String("country_code", country.Code),
				zap.Error(err))
			continue
		}

		// 更新国家的商家数量
		country.MerchantCount = count
		if err := s.countryService.UpdateCountry(ctx, country); err != nil {
			zap.L().Error("更新国家商家数量失败",
				zap.Uint64("country_id", country.ID),
				zap.String("country_code", country.Code),
				zap.Int64("merchant_count", count),
				zap.Error(err))
		} else {
			fmt.Printf("更新国家 %s (%s) 商家数量: %d\n", country.Name, country.Code, count)
		}
	}
}
