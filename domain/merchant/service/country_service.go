package service

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	"bonusearned/infra/cache/cachekey"
	"bonusearned/infra/cache/localcache"
	"bonusearned/infra/cache/rediscache"
	"bonusearned/infra/ecode"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// CountryService 国家服务接口
type CountryService interface {
	// CreateCountry 创建国家
	CreateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error
	// UpdateCountry 更新国家
	UpdateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error
	// GetCountryDetailById 根据ID获取国家详情
	GetCountryDetailById(ctx *gin.Context, id uint64) (*entity.Country, *ecode.Error)
	// GetCountryDetailByCode 根据代码获取国家详情
	GetCountryDetailByCode(ctx *gin.Context, code string) (*entity.Country, *ecode.Error)
	// GetCountryListByCondition 获取国家列表
	GetCountryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Country, int64, *ecode.Error)
	// DeleteCountry 删除国家
	DeleteCountry(ctx *gin.Context, id uint64) *ecode.Error
}

type CountryServiceImpl struct {
	repo       repository.CountryRepository
	redis      *redis.Client
	localCache *localcache.Cache
	redisCache *rediscache.Cache
	logger     *zap.Logger
}

// NewCountryService 创建国家服务
func NewCountryService(repo repository.CountryRepository, logger *zap.Logger, redis *redis.Client) CountryService {
	return &CountryServiceImpl{
		repo:       repo,
		redis:      redis,
		localCache: localcache.New(),
		redisCache: rediscache.New(redis),
		logger:     logger,
	}
}

// CreateCountry 创建国家
func (s *CountryServiceImpl) CreateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error {
	if country == nil {
		return ecode.ErrInvalidParameter
	}

	// 检查国家代码是否已存在
	existingCountry, err := s.repo.GetCountryDetailByCode(ctx, country.Code)
	if err != nil && err != ecode.ErrCountryNotFound {
		return err
	}
	if existingCountry != nil {
		return ecode.New(409, "country code already exists")
	}

	if err := s.repo.CreateCountry(ctx, country); err != nil {
		s.logger.Error("Failed to create country",
			zap.Any("country", country),
			zap.Error(err))
		return err
	}

	// 创建成功后，清除相关缓存
	s.clearCountryListCache(ctx)

	return nil
}

// UpdateCountry 更新国家
func (s *CountryServiceImpl) UpdateCountry(ctx *gin.Context, country *entity.Country) *ecode.Error {
	if country == nil {
		return ecode.ErrInvalidParameter
	}

	if err := s.repo.UpdateCountry(ctx, country); err != nil {
		s.logger.Error("Failed to update country",
			zap.Any("country", country),
			zap.Error(err))
		return err
	}

	// 更新成功后，清除相关缓存
	s.clearCountryCache(ctx, country.ID, country.Code)
	s.clearCountryListCache(ctx)

	return nil
}

// GetCountryDetailById 根据ID获取国家详情
func (s *CountryServiceImpl) GetCountryDetailById(ctx *gin.Context, id uint64) (*entity.Country, *ecode.Error) {
	if id == 0 {
		return nil, ecode.ErrInvalidID
	}

	// 生成缓存键
	cacheKey := cachekey.GenerateCountryIdKey(id)

	// 1. 尝试从本地缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if country, ok := value.(*entity.Country); ok {
			return country, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if country, ok := value.(*entity.Country); ok {
			// 设置本地缓存
			s.localCache.Set(ctx, cacheKey, country, cachekey.LongExpiration)
			return country, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	// 3. 从数据库获取
	country, err := s.repo.GetCountryDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get country by id",
			zap.Uint64("id", id),
			zap.Error(err))
		return nil, err
	}

	// 设置缓存
	s.localCache.Set(ctx, cacheKey, country, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, country, cachekey.LongExpiration)

	return country, nil
}

// GetCountryDetailByCode 根据代码获取国家详情
func (s *CountryServiceImpl) GetCountryDetailByCode(ctx *gin.Context, code string) (*entity.Country, *ecode.Error) {
	if code == "" {
		return nil, ecode.ErrInvalidParameter
	}

	// 生成缓存键
	cacheKey := cachekey.GenerateCountryCodeKey(code)

	// 1. 尝试从本地缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if country, ok := value.(*entity.Country); ok {
			return country, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if country, ok := value.(*entity.Country); ok {
			// 设置本地缓存
			s.localCache.Set(ctx, cacheKey, country, cachekey.LongExpiration)
			return country, nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	// 3. 从数据库获取
	country, err := s.repo.GetCountryDetailByCode(ctx, code)
	if err != nil {
		s.logger.Error("Failed to get country by code",
			zap.String("code", code),
			zap.Error(err))
		return nil, err
	}

	// 设置缓存
	s.localCache.Set(ctx, cacheKey, country, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, country, cachekey.LongExpiration)

	return country, nil
}

// GetCountryListByCondition 获取国家列表
func (s *CountryServiceImpl) GetCountryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Country, int64, *ecode.Error) {
	// 生成缓存键
	cacheKey := cachekey.GenerateCountryListKey(condition)

	// 1. 尝试从本地缓存获取
	if value, exists := s.localCache.Get(ctx, cacheKey); exists {
		if countryList, ok := value.([]*entity.Country); ok {
			return countryList, int64(len(countryList)), nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	// 2. 尝试从Redis缓存获取
	if value, exists := s.redisCache.Get(ctx, cacheKey); exists {
		if countryList, ok := value.([]*entity.Country); ok {
			// 设置本地缓存
			s.localCache.Set(ctx, cacheKey, countryList, cachekey.LongExpiration)
			return countryList, int64(len(countryList)), nil
		}
		s.logger.Warn("Cache type assertion failed",
			zap.String("cache_key", cacheKey),
			zap.String("type", fmt.Sprintf("%T", value)))
	}

	// 3. 从数据库获取
	countries, total, err := s.repo.GetCountryListByCondition(ctx, condition)
	if err != nil {
		s.logger.Error("Failed to get country list",
			zap.Any("condition", condition),
			zap.Error(err))
		return nil, 0, err
	}

	// 设置缓存
	s.localCache.Set(ctx, cacheKey, countries, cachekey.LongExpiration)
	s.redisCache.Set(ctx, cacheKey, countries, cachekey.LongExpiration)

	return countries, total, nil
}

// DeleteCountry 删除国家
func (s *CountryServiceImpl) DeleteCountry(ctx *gin.Context, id uint64) *ecode.Error {
	if id == 0 {
		return ecode.ErrInvalidID
	}

	// 先获取国家信息，用于清除缓存
	country, err := s.repo.GetCountryDetailById(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get country before delete",
			zap.Uint64("id", id),
			zap.Error(err))
		return err
	}

	if err := s.repo.DeleteCountry(ctx, id); err != nil {
		s.logger.Error("Failed to delete country",
			zap.Uint64("id", id),
			zap.Error(err))
		return err
	}

	// 删除成功后，清除相关缓存
	s.clearCountryCache(ctx, country.ID, country.Code)
	s.clearCountryListCache(ctx)

	return nil
}

// clearCountryCache 清除单个国家的缓存
func (s *CountryServiceImpl) clearCountryCache(ctx *gin.Context, id uint64, code string) {
	// 清除ID缓存
	idCacheKey := cachekey.GenerateCountryIdKey(id)
	s.localCache.Delete(ctx, idCacheKey)
	s.redisCache.Delete(ctx, idCacheKey)

	// 清除代码缓存
	codeCacheKey := cachekey.GenerateCountryCodeKey(code)
	s.localCache.Delete(ctx, codeCacheKey)
	s.redisCache.Delete(ctx, codeCacheKey)
}

// clearCountryListCache 清除国家列表缓存
func (s *CountryServiceImpl) clearCountryListCache(ctx *gin.Context) {
	// 由于列表缓存的key是根据条件生成的，我们需要清除所有可能的列表缓存
	// 这里可以使用通配符删除，但为了简单起见，我们清除常见的缓存key

	// 清除所有国家列表缓存
	allCountriesKey := cachekey.GenerateCountryListKey(nil)
	s.localCache.Delete(ctx, allCountriesKey)
	s.redisCache.Delete(ctx, allCountriesKey)

	// 清除启用状态的国家列表缓存
	activeCondition := map[string]interface{}{"status": 1}
	activeCountriesKey := cachekey.GenerateCountryListKey(activeCondition)
	s.localCache.Delete(ctx, activeCountriesKey)
	s.redisCache.Delete(ctx, activeCountriesKey)
}
