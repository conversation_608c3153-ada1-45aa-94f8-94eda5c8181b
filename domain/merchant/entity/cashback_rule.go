package entity

import (
	"time"

	"github.com/shopspring/decimal"
)

// CashbackRule 商家返现规则实体
type CashbackRule struct {
	ID         uint64 `gorm:"primarykey" json:"id"`
	UserID     uint64 `gorm:"index:idx_user_global,priority:1;index:idx_user_merchant,priority:1;not null" json:"user_id"`
	MerchantID uint64 `gorm:"index:idx_user_merchant,priority:2;default:0" json:"merchant_id"` // 全局规则时为0
	//CashbackValue decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"cashback_value"` // 返现值
	CashbackRate decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"cashback_rate"`                // 返现比例
	IsGlobal     bool            `gorm:"index:idx_user_global,priority:2;default:false" json:"is_global"` // 全局返现
	StartTime    time.Time       `json:"start_time"`                                                      // 生效开始时间
	EndTime      time.Time       `json:"end_time"`                                                        // 生效结束时间
	Status       int8            `gorm:"type:smallint;default:1;not null" json:"status"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
}

// IsActive 检查商家返现规则是否生效
func (r *CashbackRule) IsActive() bool {
	now := time.Now()
	return r.Status == 1 && now.After(r.StartTime) && now.Before(r.EndTime)
}
