time="2025-04-07T00:31:43+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:31:48+08:00" level=info msg="项目构建完成"
time="2025-04-07T00:31:48+08:00" level=info msg="开始上传前端文件..."
time="2025-04-07T00:31:52+08:00" level=warning msg="设置文件权限失败" error="connection lost" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/vite.svg
time="2025-04-07T00:31:52+08:00" level=warning msg="设置文件权限失败" error="connection lost" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/registerSW.js
time="2025-04-07T00:31:52+08:00" level=warning msg="设置文件权限失败" error="connection lost" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/favicon.svg
time="2025-04-07T00:31:52+08:00" level=warning msg="设置文件权限失败" error="connection lost" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/logo.jpg
time="2025-04-07T00:31:52+08:00" level=warning msg="设置文件权限失败" error="connection lost" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/robots.txt
time="2025-04-07T00:31:52+08:00" level=warning msg="设置文件权限失败" error="connection lost" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/manifest.webmanifest
time="2025-04-07T00:31:52+08:00" level=warning msg="设置文件权限失败" error="connection lost" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/index.html
time="2025-04-07T00:31:52+08:00" level=warning msg="设置文件权限失败" error="connection lost" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/sw.js
time="2025-04-07T00:31:52+08:00" level=error msg="写入远程文件失败 /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/registerSW.js: connection lost"
time="2025-04-07T00:31:55+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148: sftp: \"Failure\" (SSH_FX_FAILURE)"
time="2025-04-07T00:31:55+08:00" level=fatal msg="上传前端文件失败: 上传过程中发生错误: 写入远程文件失败 /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003148/registerSW.js: connection lost"
time="2025-04-07T00:32:00+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:32:06+08:00" level=info msg="项目构建完成"
time="2025-04-07T00:32:06+08:00" level=info msg="开始上传前端文件..."
time="2025-04-07T00:32:13+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_003212: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_003212
time="2025-04-07T00:32:17+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003206 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003206: file does not exist"
time="2025-04-07T00:32:17+08:00" level=info msg="前端文件上传完成"
time="2025-04-07T00:32:17+08:00" level=info msg="开始上传后端文件..."
time="2025-04-07T00:34:32+08:00" level=info msg="后端migrate文件上传完成"
time="2025-04-07T00:37:36+08:00" level=info msg="后端task文件上传完成"
time="2025-04-07T00:38:10+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:38:22+08:00" level=fatal msg="项目构建失败: exit status 2\nCGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/api cmd/api/main.go cmd/api/wire_gen.go\nCGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/track cmd/track/main.go\nCGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/task cmd/task/main.go\nCGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/migrate cmd/migrate/main.go\n/Library/Developer/CommandLineTools/usr/bin/make frontend-build-prod\ncd frontend-react && npm install && npm run build:live\nnpm warn ERESOLVE overriding peer dependency\nnpm warn While resolving: react-side-effect@2.1.2\nnpm warn Found: react@19.0.0\nnpm warn node_modules/react\nnpm warn   react@\"^19.0.0\" from the root project\nnpm warn   22 more (@floating-ui/react, @floating-ui/react-dom, ...)\nnpm warn\nnpm warn Could not resolve dependency:\nnpm warn peer react@\"^16.3.0 || ^17.0.0 || ^18.0.0\" from react-side-effect@2.1.2\nnpm warn node_modules/react-helmet/node_modules/react-side-effect\nnpm warn   react-side-effect@\"^2.1.0\" from react-helmet@6.1.0\nnpm warn   node_modules/react-helmet\nnpm warn\nnpm warn Conflicting peer dependency: react@18.3.1\nnpm warn node_modules/react\nnpm warn   peer react@\"^16.3.0 || ^17.0.0 || ^18.0.0\" from react-side-effect@2.1.2\nnpm warn   node_modules/react-helmet/node_modules/react-side-effect\nnpm warn     react-side-effect@\"^2.1.0\" from react-helmet@6.1.0\nnpm warn     node_modules/react-helmet\nnpm warn ERESOLVE overriding peer dependency\nnpm warn While resolving: react-html-parser@2.0.2\nnpm warn Found: react@19.0.0\nnpm warn node_modules/react\nnpm warn   react@\"^19.0.0\" from the root project\nnpm warn   22 more (@floating-ui/react, @floating-ui/react-dom, ...)\nnpm warn\nnpm warn Could not resolve dependency:\nnpm warn peer react@\"^0.14.0 || ^15.0.0 || ^16.0.0-0\" from react-html-parser@2.0.2\nnpm warn node_modules/react-simple-captcha/node_modules/react-html-parser\nnpm warn   react-html-parser@\"^2.0.2\" from react-simple-captcha@9.3.1\nnpm warn   node_modules/react-simple-captcha\nnpm warn\nnpm warn Conflicting peer dependency: react@16.14.0\nnpm warn node_modules/react\nnpm warn   peer react@\"^0.14.0 || ^15.0.0 || ^16.0.0-0\" from react-html-parser@2.0.2\nnpm warn   node_modules/react-simple-captcha/node_modules/react-html-parser\nnpm warn     react-html-parser@\"^2.0.2\" from react-simple-captcha@9.3.1\nnpm warn     node_modules/react-simple-captcha\n\nup to date, audited 859 packages in 7s\n\n222 packages are looking for funding\n  run `npm fund` for details\n\n33 vulnerabilities (4 moderate, 29 high)\n\nTo address issues that do not require attention, run:\n  npm audit fix\n\nTo address all issues (including breaking changes), run:\n  npm audit fix --force\n\nRun `npm audit` for details.\n\n> frontend-react@0.0.0 build:live\n> cross-env NODE_ENV=production tsc && vite build --mode live\n\n(!) the `splitVendorChunk` plugin doesn't have any effect when using the object form of `build.rollupOptions.output.manualChunks`. Consider using the function form instead.\nvite v5.4.11 building for live...\ntransforming...\n✓ 2 modules transformed.\n\n✨ [vite-plugin-imagemin]- compressed image resource successfully: \ndist/images/pattern.svg  0%  0.32kb / tiny: 0.32kb\ndist/favicon.svg         0%  0.96kb / tiny: 0.96kb\ndist/vite.svg            0%  0.55kb / tiny: 0.55kb\ndist/logo.jpg            0%  6.01kb / tiny: 6.06kb\n\n\n\n✨ [vite-plugin-compression]:algorithm=gzip - compressed file successfully: \ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Blog-C_5gr49y.js.gz              10.79kb / gzip: 3.53kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/detail-Dz2N8Eh5.css.gz              5.06kb / gzip: 1.44kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Contact-Brx6MnEh.js.gz           4.78kb / gzip: 1.63kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/HelpCenter-DuFdf2V2.js.gz        6.21kb / gzip: 2.54kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Coupons-CoIMlDk5.js.gz           15.23kb / gzip: 4.82kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/PrivacyPolicy-CXE0wjMn.js.gz     8.69kb / gzip: 2.39kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/TermsOfService-C20hvuYV.js.gz    11.71kb / gzip: 3.60kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/StoreCard-BZRATgVN.js.gz         4.28kb / gzip: 1.56kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-DsVcsnyR.js.gz             12.85kb / gzip: 3.93kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/index-VHz65XmP.css.gz               90.77kb / gzip: 14.37kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Login-DkVqg2pN.js.gz             21.64kb / gzip: 4.82kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-Dh9s_fqF.js.gz             19.80kb / gzip: 4.46kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/ShoppingTrips-BAWx17TI.js.gz     12.68kb / gzip: 3.34kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-DHHkUPwg.js.gz             25.39kb / gzip: 4.70kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/react-vendor-BUV1AM4h.js.gz      11.72kb / gzip: 4.19kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/utils-vendor-DiAmyxZ1.js.gz      6.93kb / gzip: 2.98kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/syntax-vendor-D4lginRY.js.gz     18.58kb / gzip: 6.91kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/detail--jQKnsBh.js.gz            40.14kb / gzip: 11.34kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Register-DWyWuYlY.js.gz          27.57kb / gzip: 5.53kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/router-vendor-CBg9Ng2r.js.gz     60.87kb / gzip: 20.03kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/ui-vendor-DQw3Qjm1.js.gz         119.26kb / gzip: 38.20kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/markdown-vendor-DnzAymkC.js.gz   318.74kb / gzip: 94.23kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/BlogDetail-hd_iPY0s.js.gz        651.50kb / gzip: 230.10kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-B5mjGe3z.js.gz             1524.96kb / gzip: 500.41kb\n\n\n\n✨ [vite-plugin-compression]:algorithm=brotliCompress - compressed file successfully: \ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Contact-Brx6MnEh.js.br           4.78kb / brotliCompress: 1.38kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Blog-C_5gr49y.js.br              10.79kb / brotliCompress: 3.10kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/HelpCenter-DuFdf2V2.js.br        6.21kb / brotliCompress: 2.02kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Coupons-CoIMlDk5.js.br           15.23kb / brotliCompress: 4.21kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/PrivacyPolicy-CXE0wjMn.js.br     8.69kb / brotliCompress: 1.79kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/detail-Dz2N8Eh5.css.br              5.06kb / brotliCompress: 1.20kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Login-DkVqg2pN.js.br             21.64kb / brotliCompress: 4.17kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Register-DWyWuYlY.js.br          27.57kb / brotliCompress: 4.74kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/TermsOfService-C20hvuYV.js.br    11.71kb / brotliCompress: 2.68kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/detail--jQKnsBh.js.br            40.14kb / brotliCompress: 9.99kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-Dh9s_fqF.js.br             19.80kb / brotliCompress: 3.86kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-DsVcsnyR.js.br             12.85kb / brotliCompress: 3.52kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/ShoppingTrips-BAWx17TI.js.br     12.68kb / brotliCompress: 2.91kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/StoreCard-BZRATgVN.js.br         4.28kb / brotliCompress: 1.37kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/react-vendor-BUV1AM4h.js.br      11.72kb / brotliCompress: 3.73kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-DHHkUPwg.js.br             25.39kb / brotliCompress: 4.16kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/index-VHz65XmP.css.br               90.77kb / brotliCompress: 12.00kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/syntax-vendor-D4lginRY.js.br     18.58kb / brotliCompress: 6.30kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/utils-vendor-DiAmyxZ1.js.br      6.93kb / brotliCompress: 2.72kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/router-vendor-CBg9Ng2r.js.br     60.87kb / brotliCompress: 17.82kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/ui-vendor-DQw3Qjm1.js.br         119.26kb / brotliCompress: 34.03kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/markdown-vendor-DnzAymkC.js.br   318.74kb / brotliCompress: 78.14kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/BlogDetail-hd_iPY0s.js.br        651.50kb / brotliCompress: 175.02kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-B5mjGe3z.js.br             1524.96kb / brotliCompress: 411.09kb\n\n\nx Build failed in 2.65s\nerror during build:\n[vite:react-babel] Cannot find package '@babel/preset-env' imported from /Users/<USER>/projects/bonusearned/frontend-react/babel-virtual-resolve-base.js\nfile: /Users/<USER>/projects/bonusearned/frontend-react/src/main.tsx\n    at __node_internal_ (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:225:9)\n    at new NodeError (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:195:5)\n    at packageResolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:899:9)\n    at moduleResolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:939:18)\n    at defaultResolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1017:15)\n    at resolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1030:12)\n    at tryImportMetaResolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/files/plugins.js:148:45)\n    at resolveStandardizedNameForImport (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/files/plugins.js:173:19)\n    at resolveStandardizedName (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/files/plugins.js:185:22)\n    at loadPreset (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/files/plugins.js:67:7)\n    at loadPreset.next (<anonymous>)\n    at createDescriptor (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-descriptors.js:140:16)\n    at createDescriptor.next (<anonymous>)\n    at step (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:261:32)\n    at evaluateAsync (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:291:5)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:44:11\n    at Array.forEach (<anonymous>)\n    at Function.async (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:43:15)\n    at Function.all (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:216:13)\n    at Generator.next (<anonymous>)\n    at createDescriptors (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-descriptors.js:102:41)\n    at createDescriptors.next (<anonymous>)\n    at createPresetDescriptors (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-descriptors.js:96:17)\n    at createPresetDescriptors.next (<anonymous>)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-descriptors.js:57:32\n    at Generator.next (<anonymous>)\n    at Function.<anonymous> (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/gensync-utils/async.js:21:3)\n    at Generator.next (<anonymous>)\n    at step (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:269:25)\n    at evaluateAsync (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:291:5)\n    at Function.errback (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:113:7)\n    at errback (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/gensync-utils/async.js:65:18)\n    at async (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:188:17)\n    at onFirstPause (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:216:13)\n    at Generator.next (<anonymous>)\n    at cachedFunction (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/caching.js:52:46)\n    at cachedFunction.next (<anonymous>)\n    at mergeChainOpts (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-chain.js:350:34)\n    at mergeChainOpts.next (<anonymous>)\n    at chainWalker (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-chain.js:316:14)\n    at chainWalker.next (<anonymous>)\n    at buildRootChain (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-chain.js:56:36)\n    at buildRootChain.next (<anonymous>)\n    at loadPrivatePartialConfig (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/partial.js:72:62)\n    at loadPrivatePartialConfig.next (<anonymous>)\n    at loadFullConfig (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/full.js:36:46)\n    at loadFullConfig.next (<anonymous>)\n    at transform (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/transform.js:20:44)\n    at transform.next (<anonymous>)\n    at step (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:269:25)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:273:13\n    at async.call.result.err.err (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:223:11)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:189:28\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/gensync-utils/async.js:67:7\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:113:33\n    at step (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:287:14)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:273:13\n    at async.call.result.err.err (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:223:11)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:37:40\nmake[1]: *** [frontend-build-prod] Error 1\nmake: *** [build] Error 2\n"
time="2025-04-07T00:38:29+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:38:34+08:00" level=fatal msg="项目构建失败: exit status 2\nCGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/api cmd/api/main.go cmd/api/wire_gen.go\nCGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/track cmd/track/main.go\nCGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/task cmd/task/main.go\nCGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o bin/migrate cmd/migrate/main.go\n/Library/Developer/CommandLineTools/usr/bin/make frontend-build-prod\ncd frontend-react && npm install && npm run build:live\nnpm warn ERESOLVE overriding peer dependency\nnpm warn While resolving: react-side-effect@2.1.2\nnpm warn Found: react@19.0.0\nnpm warn node_modules/react\nnpm warn   react@\"^19.0.0\" from the root project\nnpm warn   22 more (@floating-ui/react, @floating-ui/react-dom, ...)\nnpm warn\nnpm warn Could not resolve dependency:\nnpm warn peer react@\"^16.3.0 || ^17.0.0 || ^18.0.0\" from react-side-effect@2.1.2\nnpm warn node_modules/react-helmet/node_modules/react-side-effect\nnpm warn   react-side-effect@\"^2.1.0\" from react-helmet@6.1.0\nnpm warn   node_modules/react-helmet\nnpm warn\nnpm warn Conflicting peer dependency: react@18.3.1\nnpm warn node_modules/react\nnpm warn   peer react@\"^16.3.0 || ^17.0.0 || ^18.0.0\" from react-side-effect@2.1.2\nnpm warn   node_modules/react-helmet/node_modules/react-side-effect\nnpm warn     react-side-effect@\"^2.1.0\" from react-helmet@6.1.0\nnpm warn     node_modules/react-helmet\nnpm warn ERESOLVE overriding peer dependency\nnpm warn While resolving: react-html-parser@2.0.2\nnpm warn Found: react@19.0.0\nnpm warn node_modules/react\nnpm warn   react@\"^19.0.0\" from the root project\nnpm warn   22 more (@floating-ui/react, @floating-ui/react-dom, ...)\nnpm warn\nnpm warn Could not resolve dependency:\nnpm warn peer react@\"^0.14.0 || ^15.0.0 || ^16.0.0-0\" from react-html-parser@2.0.2\nnpm warn node_modules/react-simple-captcha/node_modules/react-html-parser\nnpm warn   react-html-parser@\"^2.0.2\" from react-simple-captcha@9.3.1\nnpm warn   node_modules/react-simple-captcha\nnpm warn\nnpm warn Conflicting peer dependency: react@16.14.0\nnpm warn node_modules/react\nnpm warn   peer react@\"^0.14.0 || ^15.0.0 || ^16.0.0-0\" from react-html-parser@2.0.2\nnpm warn   node_modules/react-simple-captcha/node_modules/react-html-parser\nnpm warn     react-html-parser@\"^2.0.2\" from react-simple-captcha@9.3.1\nnpm warn     node_modules/react-simple-captcha\n\nup to date, audited 859 packages in 1s\n\n222 packages are looking for funding\n  run `npm fund` for details\n\n33 vulnerabilities (4 moderate, 29 high)\n\nTo address issues that do not require attention, run:\n  npm audit fix\n\nTo address all issues (including breaking changes), run:\n  npm audit fix --force\n\nRun `npm audit` for details.\n\n> frontend-react@0.0.0 build:live\n> cross-env NODE_ENV=production tsc && vite build --mode live\n\n(!) the `splitVendorChunk` plugin doesn't have any effect when using the object form of `build.rollupOptions.output.manualChunks`. Consider using the function form instead.\nvite v5.4.11 building for live...\ntransforming...\n✓ 2 modules transformed.\n\n✨ [vite-plugin-imagemin]- compressed image resource successfully: \ndist/vite.svg            0%   0.55kb / tiny: 0.55kb\ndist/images/pattern.svg  0%   0.32kb / tiny: 0.32kb\ndist/favicon.svg         0%   0.96kb / tiny: 0.96kb\ndist/logo.jpg            -1%  6.06kb / tiny: 6.04kb\n\n\n\n✨ [vite-plugin-compression]:algorithm=gzip - compressed file successfully: \ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/detail-Dz2N8Eh5.css.gz              5.06kb / gzip: 1.44kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Blog-C_5gr49y.js.gz              10.79kb / gzip: 3.53kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/HelpCenter-DuFdf2V2.js.gz        6.21kb / gzip: 2.54kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Contact-Brx6MnEh.js.gz           4.78kb / gzip: 1.63kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/PrivacyPolicy-CXE0wjMn.js.gz     8.69kb / gzip: 2.39kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/StoreCard-BZRATgVN.js.gz         4.28kb / gzip: 1.56kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Login-DkVqg2pN.js.gz             21.64kb / gzip: 4.82kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Register-DWyWuYlY.js.gz          27.57kb / gzip: 5.53kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/ShoppingTrips-BAWx17TI.js.gz     12.68kb / gzip: 3.34kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/TermsOfService-C20hvuYV.js.gz    11.71kb / gzip: 3.60kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Coupons-CoIMlDk5.js.gz           15.23kb / gzip: 4.82kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-DHHkUPwg.js.gz             25.39kb / gzip: 4.70kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-Dh9s_fqF.js.gz             19.80kb / gzip: 4.46kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/utils-vendor-DiAmyxZ1.js.gz      6.93kb / gzip: 2.98kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/syntax-vendor-D4lginRY.js.gz     18.58kb / gzip: 6.91kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/index-VHz65XmP.css.gz               90.77kb / gzip: 14.37kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/detail--jQKnsBh.js.gz            40.14kb / gzip: 11.34kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/react-vendor-BUV1AM4h.js.gz      11.72kb / gzip: 4.19kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-DsVcsnyR.js.gz             12.85kb / gzip: 3.93kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/router-vendor-CBg9Ng2r.js.gz     60.87kb / gzip: 20.03kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/ui-vendor-DQw3Qjm1.js.gz         119.26kb / gzip: 38.20kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/markdown-vendor-DnzAymkC.js.gz   318.74kb / gzip: 94.23kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/BlogDetail-hd_iPY0s.js.gz        651.50kb / gzip: 230.10kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-B5mjGe3z.js.gz             1524.96kb / gzip: 500.41kb\n\n\n\n✨ [vite-plugin-compression]:algorithm=brotliCompress - compressed file successfully: \ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/detail-Dz2N8Eh5.css.br              5.06kb / brotliCompress: 1.20kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Contact-Brx6MnEh.js.br           4.78kb / brotliCompress: 1.38kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Coupons-CoIMlDk5.js.br           15.23kb / brotliCompress: 4.21kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/HelpCenter-DuFdf2V2.js.br        6.21kb / brotliCompress: 2.02kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Login-DkVqg2pN.js.br             21.64kb / brotliCompress: 4.17kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Register-DWyWuYlY.js.br          27.57kb / brotliCompress: 4.74kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/PrivacyPolicy-CXE0wjMn.js.br     8.69kb / brotliCompress: 1.79kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/StoreCard-BZRATgVN.js.br         4.28kb / brotliCompress: 1.37kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/ShoppingTrips-BAWx17TI.js.br     12.68kb / brotliCompress: 2.91kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-DHHkUPwg.js.br             25.39kb / brotliCompress: 4.16kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-Dh9s_fqF.js.br             19.80kb / brotliCompress: 3.86kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-DsVcsnyR.js.br             12.85kb / brotliCompress: 3.52kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/Blog-C_5gr49y.js.br              10.79kb / brotliCompress: 3.10kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/index-VHz65XmP.css.br               90.77kb / brotliCompress: 12.00kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/react-vendor-BUV1AM4h.js.br      11.72kb / brotliCompress: 3.73kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/TermsOfService-C20hvuYV.js.br    11.71kb / brotliCompress: 2.68kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/syntax-vendor-D4lginRY.js.br     18.58kb / brotliCompress: 6.30kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/detail--jQKnsBh.js.br            40.14kb / brotliCompress: 9.99kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/utils-vendor-DiAmyxZ1.js.br      6.93kb / brotliCompress: 2.72kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/router-vendor-CBg9Ng2r.js.br     60.87kb / brotliCompress: 17.82kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/ui-vendor-DQw3Qjm1.js.br         119.26kb / brotliCompress: 34.03kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/markdown-vendor-DnzAymkC.js.br   318.74kb / brotliCompress: 78.14kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/BlogDetail-hd_iPY0s.js.br        651.50kb / brotliCompress: 175.02kb\ndist//Users/<USER>/projects/bonusearned/frontend-react/assets/js/index-B5mjGe3z.js.br             1524.96kb / brotliCompress: 411.09kb\n\n\nx Build failed in 2.55s\nerror during build:\n[vite:react-babel] Cannot find package '@babel/preset-env' imported from /Users/<USER>/projects/bonusearned/frontend-react/babel-virtual-resolve-base.js\nfile: /Users/<USER>/projects/bonusearned/frontend-react/src/main.tsx\n    at __node_internal_ (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:225:9)\n    at new NodeError (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:195:5)\n    at packageResolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:899:9)\n    at moduleResolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:939:18)\n    at defaultResolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1017:15)\n    at resolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/vendor/import-meta-resolve.js:1030:12)\n    at tryImportMetaResolve (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/files/plugins.js:148:45)\n    at resolveStandardizedNameForImport (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/files/plugins.js:173:19)\n    at resolveStandardizedName (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/files/plugins.js:185:22)\n    at loadPreset (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/files/plugins.js:67:7)\n    at loadPreset.next (<anonymous>)\n    at createDescriptor (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-descriptors.js:140:16)\n    at createDescriptor.next (<anonymous>)\n    at step (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:261:32)\n    at evaluateAsync (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:291:5)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:44:11\n    at Array.forEach (<anonymous>)\n    at Function.async (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:43:15)\n    at Function.all (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:216:13)\n    at Generator.next (<anonymous>)\n    at createDescriptors (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-descriptors.js:102:41)\n    at createDescriptors.next (<anonymous>)\n    at createPresetDescriptors (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-descriptors.js:96:17)\n    at createPresetDescriptors.next (<anonymous>)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-descriptors.js:57:32\n    at Generator.next (<anonymous>)\n    at Function.<anonymous> (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/gensync-utils/async.js:21:3)\n    at Generator.next (<anonymous>)\n    at step (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:269:25)\n    at evaluateAsync (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:291:5)\n    at Function.errback (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:113:7)\n    at errback (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/gensync-utils/async.js:65:18)\n    at async (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:188:17)\n    at onFirstPause (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:216:13)\n    at Generator.next (<anonymous>)\n    at cachedFunction (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/caching.js:52:46)\n    at cachedFunction.next (<anonymous>)\n    at mergeChainOpts (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-chain.js:350:34)\n    at mergeChainOpts.next (<anonymous>)\n    at chainWalker (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-chain.js:316:14)\n    at chainWalker.next (<anonymous>)\n    at buildRootChain (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/config-chain.js:56:36)\n    at buildRootChain.next (<anonymous>)\n    at loadPrivatePartialConfig (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/partial.js:72:62)\n    at loadPrivatePartialConfig.next (<anonymous>)\n    at loadFullConfig (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/config/full.js:36:46)\n    at loadFullConfig.next (<anonymous>)\n    at transform (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/transform.js:20:44)\n    at transform.next (<anonymous>)\n    at step (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:269:25)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:273:13\n    at async.call.result.err.err (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:223:11)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:189:28\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/@babel/core/lib/gensync-utils/async.js:67:7\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:113:33\n    at step (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:287:14)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:273:13\n    at async.call.result.err.err (/Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:223:11)\n    at /Users/<USER>/projects/bonusearned/frontend-react/node_modules/gensync/index.js:37:40\nmake[1]: *** [frontend-build-prod] Error 1\nmake: *** [build] Error 2\n"
time="2025-04-07T00:39:52+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:39:56+08:00" level=info msg="项目构建完成"
time="2025-04-07T00:39:56+08:00" level=info msg="开始上传前端文件..."
time="2025-04-07T00:40:04+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004003: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004003
time="2025-04-07T00:40:07+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003956 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_003956: file does not exist"
time="2025-04-07T00:40:08+08:00" level=info msg="前端文件上传完成"
time="2025-04-07T00:40:08+08:00" level=info msg="开始上传后端文件..."
time="2025-04-07T00:40:11+08:00" level=fatal msg="上传后端track文件失败: 写入远程文件失败: connection lost"
time="2025-04-07T00:42:43+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:42:47+08:00" level=info msg="项目构建完成"
time="2025-04-07T00:42:47+08:00" level=info msg="开始上传前端文件..."
time="2025-04-07T00:42:54+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004253: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004253
time="2025-04-07T00:42:57+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_004247 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_004247: file does not exist"
time="2025-04-07T00:42:58+08:00" level=info msg="前端文件上传完成"
time="2025-04-07T00:42:58+08:00" level=info msg="开始上传后端文件..."
time="2025-04-07T00:43:56+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:44:07+08:00" level=info msg="项目构建完成"
time="2025-04-07T00:44:07+08:00" level=info msg="开始上传前端文件..."
time="2025-04-07T00:44:14+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004413: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004413
time="2025-04-07T00:44:17+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_004407 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_004407: file does not exist"
time="2025-04-07T00:44:18+08:00" level=info msg="前端文件上传完成"
time="2025-04-07T00:44:18+08:00" level=info msg="开始上传后端文件..."
time="2025-04-07T00:46:55+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:47:00+08:00" level=info msg="项目构建完成"
time="2025-04-07T00:47:00+08:00" level=info msg="开始上传前端文件..."
time="2025-04-07T00:47:08+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004707: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004707
time="2025-04-07T00:47:11+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_004700 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_004700: file does not exist"
time="2025-04-07T00:47:11+08:00" level=info msg="前端文件上传完成"
time="2025-04-07T00:47:11+08:00" level=info msg="开始上传后端文件..."
time="2025-04-07T00:49:23+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:49:33+08:00" level=info msg="项目构建完成"
time="2025-04-07T00:49:33+08:00" level=info msg="开始上传前端文件..."
time="2025-04-07T00:49:40+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004939: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_004939
time="2025-04-07T00:49:43+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_004933 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_004933: file does not exist"
time="2025-04-07T00:49:43+08:00" level=info msg="前端文件上传完成"
time="2025-04-07T00:49:43+08:00" level=info msg="开始上传后端文件..."
time="2025-04-07T00:52:47+08:00" level=info msg="后端api文件上传完成"
time="2025-04-07T00:54:21+08:00" level=info msg="开始构建项目..."
time="2025-04-07T00:54:25+08:00" level=info msg="项目构建完成"
time="2025-04-07T00:54:25+08:00" level=info msg="开始上传前端文件..."
time="2025-04-07T00:54:32+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_005431: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250407_005431
time="2025-04-07T00:54:35+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_005425 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250407_005425: file does not exist"
time="2025-04-07T00:54:35+08:00" level=info msg="前端文件上传完成"
time="2025-04-07T00:54:35+08:00" level=info msg="开始上传后端文件..."
time="2025-04-07T00:54:38+08:00" level=fatal msg="上传后端track文件失败: 写入远程文件失败: connection lost"
time="2025-04-11T15:30:28+08:00" level=info msg="开始构建项目..."
time="2025-04-11T15:30:44+08:00" level=info msg="项目构建完成"
time="2025-04-11T15:30:44+08:00" level=info msg="开始上传前端文件..."
time="2025-04-11T15:31:13+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250411_153112: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250411_153112
time="2025-04-11T15:31:16+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250411_153044 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250411_153044: file does not exist"
time="2025-04-11T15:31:17+08:00" level=info msg="前端文件上传完成"
time="2025-04-11T15:31:17+08:00" level=info msg="开始上传后端文件..."
time="2025-04-11T15:34:31+08:00" level=info msg="后端api文件上传完成"
time="2025-04-11T15:36:56+08:00" level=info msg="后端migrate文件上传完成"
time="2025-04-11T15:40:24+08:00" level=info msg="后端task文件上传完成"
time="2025-04-11T15:44:16+08:00" level=info msg="后端track文件上传完成"
time="2025-04-11T15:56:31+08:00" level=info msg="开始构建项目..."
time="2025-04-11T15:56:49+08:00" level=info msg="开始构建项目..."
time="2025-04-11T15:57:06+08:00" level=info msg="项目构建完成"
time="2025-04-11T15:57:06+08:00" level=info msg="开始上传前端文件..."
time="2025-04-11T15:57:35+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250411_155734: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250411_155734
time="2025-04-11T15:57:38+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250411_155706 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250411_155706: file does not exist"
time="2025-04-11T15:57:39+08:00" level=info msg="前端文件上传完成"
time="2025-04-12T13:53:25+08:00" level=info msg="开始构建项目..."
time="2025-04-12T13:53:43+08:00" level=info msg="项目构建完成"
time="2025-04-12T13:53:43+08:00" level=info msg="开始上传前端文件..."
time="2025-04-12T13:54:15+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250412_135414: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250412_135414
time="2025-04-12T13:54:18+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250412_135343 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250412_135343: file does not exist"
time="2025-04-12T13:54:18+08:00" level=info msg="前端文件上传完成"
time="2025-05-12T19:52:39+08:00" level=info msg="开始构建项目..."
time="2025-05-12T19:52:58+08:00" level=info msg="项目构建完成"
time="2025-05-12T19:52:58+08:00" level=info msg="开始上传前端文件..."
time="2025-05-12T19:53:29+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250512_195328: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250512_195328
time="2025-05-12T19:53:32+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250512_195258 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250512_195258: file does not exist"
time="2025-05-12T19:53:32+08:00" level=info msg="前端文件上传完成"
time="2025-05-12T19:54:57+08:00" level=info msg="开始构建项目..."
time="2025-05-12T19:55:15+08:00" level=info msg="项目构建完成"
time="2025-05-12T19:55:15+08:00" level=info msg="开始上传前端文件..."
time="2025-05-12T19:55:45+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250512_195544: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250512_195544
time="2025-05-12T19:55:48+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250512_195515 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250512_195515: file does not exist"
time="2025-05-12T19:55:48+08:00" level=info msg="前端文件上传完成"
time="2025-05-12T19:55:48+08:00" level=info msg="开始上传后端文件..."
time="2025-05-12T19:59:07+08:00" level=info msg="开始构建项目..."
time="2025-05-12T19:59:24+08:00" level=info msg="项目构建完成"
time="2025-05-12T19:59:24+08:00" level=info msg="开始上传前端文件..."
time="2025-05-12T19:59:54+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250512_195953: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250512_195953
time="2025-05-12T19:59:58+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250512_195924 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250512_195924: file does not exist"
time="2025-05-12T19:59:58+08:00" level=info msg="前端文件上传完成"
time="2025-05-12T19:59:58+08:00" level=info msg="开始上传后端文件..."
time="2025-05-12T20:03:39+08:00" level=info msg="后端api文件上传完成"
time="2025-05-12T20:06:22+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-12T20:09:52+08:00" level=info msg="后端task文件上传完成"
time="2025-05-12T20:13:14+08:00" level=info msg="后端track文件上传完成"
time="2025-05-13T00:29:10+08:00" level=info msg="开始构建项目..."
time="2025-05-13T00:29:30+08:00" level=info msg="项目构建完成"
time="2025-05-13T00:29:30+08:00" level=info msg="开始上传前端文件..."
time="2025-05-13T00:29:59+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250513_002958: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250513_002958
time="2025-05-13T00:30:03+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250513_002930 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250513_002930: file does not exist"
time="2025-05-13T00:30:03+08:00" level=info msg="前端文件上传完成"
time="2025-05-13T00:30:03+08:00" level=info msg="开始上传后端文件..."
time="2025-05-13T00:33:20+08:00" level=info msg="后端track文件上传完成"
time="2025-05-13T00:36:43+08:00" level=info msg="后端api文件上传完成"
time="2025-05-13T00:39:12+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-13T00:42:29+08:00" level=info msg="后端task文件上传完成"
time="2025-05-15T11:59:09+08:00" level=info msg="开始构建项目..."
time="2025-05-15T11:59:37+08:00" level=info msg="项目构建完成"
time="2025-05-15T11:59:37+08:00" level=info msg="开始上传前端文件..."
time="2025-05-15T11:59:37+08:00" level=info msg="开始构建项目..."
time="2025-05-15T11:59:45+08:00" level=info msg="开始构建项目..."
time="2025-05-15T11:59:50+08:00" level=error msg="打开本地文件失败 ../../frontend-react/dist/assets/js/syntax-vendor-CZfYkhNh.js: open ../../frontend-react/dist/assets/js/syntax-vendor-CZfYkhNh.js: no such file or directory"
time="2025-05-15T11:59:53+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_115937 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_115937: sftp: \"Failure\" (SSH_FX_FAILURE)"
time="2025-05-15T11:59:53+08:00" level=fatal msg="上传前端文件失败: 上传过程中发生错误: 打开本地文件失败 ../../frontend-react/dist/assets/js/syntax-vendor-CZfYkhNh.js: open ../../frontend-react/dist/assets/js/syntax-vendor-CZfYkhNh.js: no such file or directory"
time="2025-05-15T12:00:12+08:00" level=info msg="项目构建完成"
time="2025-05-15T12:00:12+08:00" level=info msg="开始上传前端文件..."
time="2025-05-15T12:00:39+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250515_120038: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250515_120038
time="2025-05-15T12:00:43+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_120012 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_120012: file does not exist"
time="2025-05-15T12:00:43+08:00" level=info msg="前端文件上传完成"
time="2025-05-15T12:00:43+08:00" level=info msg="开始上传后端文件..."
time="2025-05-15T12:04:05+08:00" level=info msg="后端api文件上传完成"
time="2025-05-15T12:06:41+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-15T12:10:08+08:00" level=info msg="后端task文件上传完成"
time="2025-05-15T12:13:36+08:00" level=info msg="后端track文件上传完成"
time="2025-05-15T18:33:27+08:00" level=info msg="开始构建项目..."
time="2025-05-15T18:33:45+08:00" level=info msg="项目构建完成"
time="2025-05-15T18:33:45+08:00" level=info msg="开始上传前端文件..."
time="2025-05-15T18:34:17+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250515_183416: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250515_183416
time="2025-05-15T18:34:21+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_183345 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_183345: file does not exist"
time="2025-05-15T18:34:21+08:00" level=info msg="前端文件上传完成"
time="2025-05-15T18:34:21+08:00" level=info msg="开始上传后端文件..."
time="2025-05-15T18:38:28+08:00" level=info msg="后端api文件上传完成"
time="2025-05-15T18:41:40+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-15T18:47:28+08:00" level=info msg="后端task文件上传完成"
time="2025-05-15T18:51:24+08:00" level=info msg="后端track文件上传完成"
time="2025-05-15T18:53:15+08:00" level=info msg="开始构建项目..."
time="2025-05-15T18:53:31+08:00" level=info msg="项目构建完成"
time="2025-05-15T18:53:31+08:00" level=info msg="开始上传前端文件..."
time="2025-05-15T18:54:03+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250515_185402: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250515_185402
time="2025-05-15T18:54:07+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_185331 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_185331: file does not exist"
time="2025-05-15T18:54:07+08:00" level=info msg="前端文件上传完成"
time="2025-05-15T18:54:07+08:00" level=info msg="开始上传后端文件..."
time="2025-05-15T18:59:53+08:00" level=info msg="后端task文件上传完成"
time="2025-05-15T19:07:56+08:00" level=info msg="开始构建项目..."
time="2025-05-15T19:08:12+08:00" level=info msg="项目构建完成"
time="2025-05-15T19:08:12+08:00" level=info msg="开始上传前端文件..."
time="2025-05-15T19:08:45+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250515_190844: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250515_190844
time="2025-05-15T19:08:49+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_190812 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250515_190812: file does not exist"
time="2025-05-15T19:08:49+08:00" level=info msg="前端文件上传完成"
time="2025-05-15T19:08:49+08:00" level=info msg="开始上传后端文件..."
time="2025-05-15T19:12:59+08:00" level=info msg="后端api文件上传完成"
time="2025-05-15T19:16:06+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-15T19:21:46+08:00" level=info msg="后端task文件上传完成"
time="2025-05-15T19:26:45+08:00" level=info msg="后端track文件上传完成"
time="2025-05-19T00:11:56+08:00" level=info msg="开始构建项目..."
time="2025-05-19T00:12:12+08:00" level=info msg="项目构建完成"
time="2025-05-19T00:12:12+08:00" level=info msg="开始上传前端文件..."
time="2025-05-19T00:12:40+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250519_001239: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250519_001239
time="2025-05-19T00:12:44+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250519_001212 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250519_001212: file does not exist"
time="2025-05-19T00:12:44+08:00" level=info msg="前端文件上传完成"
time="2025-05-19T00:12:44+08:00" level=info msg="开始上传后端文件..."
time="2025-05-19T00:15:56+08:00" level=info msg="后端api文件上传完成"
time="2025-05-19T00:18:26+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-19T00:21:45+08:00" level=info msg="后端task文件上传完成"
time="2025-05-19T00:24:46+08:00" level=info msg="后端track文件上传完成"
time="2025-05-19T14:02:36+08:00" level=info msg="开始构建项目..."
time="2025-05-19T14:02:58+08:00" level=info msg="项目构建完成"
time="2025-05-19T14:02:58+08:00" level=info msg="开始上传前端文件..."
time="2025-05-19T14:03:30+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250519_140258 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250519_140258: file does not exist"
time="2025-05-19T14:03:30+08:00" level=info msg="前端文件上传完成"
time="2025-05-19T14:03:30+08:00" level=info msg="开始上传后端文件..."
time="2025-05-19T14:07:02+08:00" level=info msg="后端api文件上传完成"
time="2025-05-19T14:09:39+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-19T14:13:12+08:00" level=info msg="后端task文件上传完成"
time="2025-05-19T14:16:37+08:00" level=info msg="后端track文件上传完成"
time="2025-05-19T22:50:57+08:00" level=info msg="开始构建项目..."
time="2025-05-19T22:51:13+08:00" level=info msg="项目构建完成"
time="2025-05-19T22:51:13+08:00" level=info msg="开始上传前端文件..."
time="2025-05-19T22:51:53+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250519_225152: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250519_225152
time="2025-05-19T22:51:56+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250519_225113 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250519_225113: file does not exist"
time="2025-05-19T22:51:57+08:00" level=info msg="前端文件上传完成"
time="2025-05-19T22:51:57+08:00" level=info msg="开始上传后端文件..."
time="2025-05-19T22:57:59+08:00" level=info msg="后端api文件上传完成"
time="2025-05-19T23:01:21+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-19T23:05:42+08:00" level=info msg="后端task文件上传完成"
time="2025-05-19T23:09:50+08:00" level=info msg="后端track文件上传完成"
time="2025-05-20T12:21:33+08:00" level=info msg="开始构建项目..."
time="2025-05-20T12:21:51+08:00" level=info msg="项目构建完成"
time="2025-05-20T12:21:51+08:00" level=info msg="开始上传前端文件..."
time="2025-05-20T12:22:21+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250520_122220: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250520_122220
time="2025-05-20T12:22:24+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250520_122151 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250520_122151: file does not exist"
time="2025-05-20T12:22:24+08:00" level=info msg="前端文件上传完成"
time="2025-05-20T12:22:24+08:00" level=info msg="开始上传后端文件..."
time="2025-05-20T12:26:08+08:00" level=info msg="后端api文件上传完成"
time="2025-05-20T12:28:51+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-20T12:32:28+08:00" level=info msg="后端task文件上传完成"
time="2025-05-20T12:35:55+08:00" level=info msg="后端track文件上传完成"
time="2025-05-20T12:38:28+08:00" level=info msg="开始构建项目..."
time="2025-05-20T12:38:45+08:00" level=info msg="项目构建完成"
time="2025-05-20T12:38:45+08:00" level=info msg="开始上传前端文件..."
time="2025-05-20T12:39:33+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250520_123932: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250520_123932
time="2025-05-20T12:39:36+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250520_123845 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250520_123845: file does not exist"
time="2025-05-20T12:39:37+08:00" level=info msg="前端文件上传完成"
time="2025-05-20T12:39:37+08:00" level=info msg="开始上传后端文件..."
time="2025-05-20T12:45:15+08:00" level=info msg="后端api文件上传完成"
time="2025-05-20T12:50:08+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-20T12:55:41+08:00" level=info msg="后端task文件上传完成"
time="2025-05-20T13:00:09+08:00" level=info msg="后端track文件上传完成"
time="2025-05-23T14:16:52+08:00" level=info msg="开始构建项目..."
time="2025-05-23T14:17:13+08:00" level=info msg="项目构建完成"
time="2025-05-23T14:17:13+08:00" level=info msg="开始上传前端文件..."
time="2025-05-23T14:17:44+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250523_141743: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250523_141743
time="2025-05-23T14:17:48+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250523_141713 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250523_141713: file does not exist"
time="2025-05-23T14:17:48+08:00" level=info msg="前端文件上传完成"
time="2025-05-23T14:17:48+08:00" level=info msg="开始上传后端文件..."
time="2025-05-24T11:39:52+08:00" level=info msg="开始构建项目..."
time="2025-05-24T11:40:07+08:00" level=info msg="项目构建完成"
time="2025-05-24T11:40:07+08:00" level=info msg="开始上传前端文件..."
time="2025-05-24T11:40:43+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250524_114042: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250524_114042
time="2025-05-24T11:40:46+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250524_114007 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250524_114007: file does not exist"
time="2025-05-24T11:40:46+08:00" level=info msg="前端文件上传完成"
time="2025-05-24T11:40:46+08:00" level=info msg="开始上传后端文件..."
time="2025-05-24T11:45:06+08:00" level=info msg="后端api文件上传完成"
time="2025-05-24T11:48:33+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-24T11:53:02+08:00" level=info msg="后端task文件上传完成"
time="2025-05-24T11:57:07+08:00" level=info msg="后端track文件上传完成"
time="2025-05-27T09:56:05+08:00" level=info msg="开始构建项目..."
time="2025-05-27T09:56:20+08:00" level=info msg="项目构建完成"
time="2025-05-27T09:56:20+08:00" level=info msg="开始上传前端文件..."
time="2025-05-27T09:56:49+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_095648: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_095648
time="2025-05-27T09:56:52+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_095620 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_095620: file does not exist"
time="2025-05-27T09:56:52+08:00" level=info msg="前端文件上传完成"
time="2025-05-27T10:02:27+08:00" level=info msg="开始构建项目..."
time="2025-05-27T10:02:42+08:00" level=info msg="项目构建完成"
time="2025-05-27T10:02:42+08:00" level=info msg="开始上传前端文件..."
time="2025-05-27T10:03:10+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_100309: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_100309
time="2025-05-27T10:03:13+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_100242 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_100242: file does not exist"
time="2025-05-27T10:03:13+08:00" level=info msg="前端文件上传完成"
time="2025-05-27T10:09:51+08:00" level=info msg="开始构建项目..."
time="2025-05-27T10:10:06+08:00" level=info msg="项目构建完成"
time="2025-05-27T10:10:06+08:00" level=info msg="开始上传前端文件..."
time="2025-05-27T10:10:35+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_101034: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_101034
time="2025-05-27T10:10:39+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_101006 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_101006: file does not exist"
time="2025-05-27T10:10:39+08:00" level=info msg="前端文件上传完成"
time="2025-05-27T10:10:39+08:00" level=info msg="开始上传后端文件..."
time="2025-05-27T10:14:22+08:00" level=info msg="后端api文件上传完成"
time="2025-05-27T10:17:14+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-27T10:21:01+08:00" level=info msg="后端task文件上传完成"
time="2025-05-27T10:24:33+08:00" level=info msg="后端track文件上传完成"
time="2025-05-27T10:43:05+08:00" level=info msg="开始构建项目..."
time="2025-05-27T10:43:26+08:00" level=info msg="项目构建完成"
time="2025-05-27T10:43:26+08:00" level=info msg="开始上传前端文件..."
time="2025-05-27T10:43:55+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_104354: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_104354
time="2025-05-27T10:43:58+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_104326 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_104326: file does not exist"
time="2025-05-27T10:43:58+08:00" level=info msg="前端文件上传完成"
time="2025-05-27T10:55:30+08:00" level=info msg="开始构建项目..."
time="2025-05-27T10:55:54+08:00" level=info msg="项目构建完成"
time="2025-05-27T10:55:54+08:00" level=info msg="开始上传前端文件..."
time="2025-05-27T10:55:59+08:00" level=warning msg="SSH连接失败，准备重试" attempt=1 error="dial tcp 152.53.243.103:22: i/o timeout"
time="2025-05-27T10:56:06+08:00" level=info msg="开始构建项目..."
time="2025-05-27T10:56:25+08:00" level=info msg="项目构建完成"
time="2025-05-27T10:56:25+08:00" level=info msg="开始上传前端文件..."
time="2025-05-27T10:56:52+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_105651: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_105651
time="2025-05-27T10:56:55+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_105625 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_105625: file does not exist"
time="2025-05-27T10:56:55+08:00" level=info msg="前端文件上传完成"
time="2025-05-27T11:08:45+08:00" level=info msg="开始构建项目..."
time="2025-05-27T11:09:23+08:00" level=info msg="项目构建完成"
time="2025-05-27T11:09:23+08:00" level=info msg="开始上传前端文件..."
time="2025-05-27T11:09:49+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_110948: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250527_110948
time="2025-05-27T11:09:52+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_110923 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250527_110923: file does not exist"
time="2025-05-27T11:09:52+08:00" level=info msg="前端文件上传完成"
time="2025-05-27T11:09:52+08:00" level=info msg="开始上传后端文件..."
time="2025-05-27T11:12:43+08:00" level=info msg="后端track文件上传完成"
time="2025-05-27T11:15:45+08:00" level=info msg="后端api文件上传完成"
time="2025-05-27T11:18:03+08:00" level=info msg="后端migrate文件上传完成"
time="2025-05-27T11:21:10+08:00" level=info msg="后端task文件上传完成"
time="2025-05-28T12:48:12+08:00" level=info msg="开始构建项目..."
time="2025-05-28T12:48:35+08:00" level=info msg="项目构建完成"
time="2025-05-28T12:48:35+08:00" level=info msg="开始上传前端文件..."
time="2025-05-28T12:49:13+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250528_124912: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250528_124912
time="2025-05-28T12:49:16+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250528_124835 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250528_124835: file does not exist"
time="2025-05-28T12:49:16+08:00" level=info msg="前端文件上传完成"
time="2025-05-28T12:49:16+08:00" level=info msg="开始上传后端文件..."
time="2025-05-28T12:54:59+08:00" level=info msg="开始构建项目..."
time="2025-05-28T12:55:21+08:00" level=info msg="项目构建完成"
time="2025-05-28T12:55:21+08:00" level=info msg="开始上传前端文件..."
time="2025-05-28T12:56:02+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250528_125601: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250528_125601
time="2025-05-28T12:56:05+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250528_125521 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250528_125521: file does not exist"
time="2025-05-28T12:56:05+08:00" level=info msg="前端文件上传完成"
time="2025-05-28T12:56:05+08:00" level=info msg="开始上传后端文件..."
time="2025-05-28T19:06:10+08:00" level=info msg="开始构建项目..."
time="2025-05-28T19:06:31+08:00" level=info msg="项目构建完成"
time="2025-05-28T19:06:31+08:00" level=info msg="开始上传前端文件..."
time="2025-05-28T19:07:01+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250528_190700: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250528_190700
time="2025-05-28T19:07:04+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250528_190631 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250528_190631: file does not exist"
time="2025-05-28T19:07:04+08:00" level=info msg="前端文件上传完成"
time="2025-05-28T19:07:04+08:00" level=info msg="开始上传后端文件..."
time="2025-05-28T19:09:45+08:00" level=info msg="开始构建项目..."
time="2025-05-28T19:10:03+08:00" level=info msg="项目构建完成"
time="2025-05-28T19:10:03+08:00" level=info msg="开始上传前端文件..."
time="2025-05-28T19:10:33+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250528_191032: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250528_191032
time="2025-05-28T19:10:36+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250528_191003 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250528_191003: file does not exist"
time="2025-05-28T19:10:36+08:00" level=info msg="前端文件上传完成"
time="2025-05-28T19:10:36+08:00" level=info msg="开始上传后端文件..."
time="2025-05-29T23:24:42+08:00" level=info msg="开始构建项目..."
time="2025-05-29T23:25:08+08:00" level=info msg="项目构建完成"
time="2025-05-29T23:25:08+08:00" level=info msg="开始上传前端文件..."
time="2025-05-29T23:25:52+08:00" level=warning msg="删除备份目录失败" error="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250529_232551: sftp: \"Failure\" (SSH_FX_FAILURE)" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/index.bak_20250529_232551
time="2025-05-29T23:25:55+08:00" level=warning msg="清理临时目录失败" path=/opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250529_232508 warn="remove /opt/1panel/apps/openresty/openresty/www/sites/bonusearned.com/.tmp_upload_20250529_232508: file does not exist"
time="2025-05-29T23:25:55+08:00" level=info msg="前端文件上传完成"
time="2025-05-29T23:25:55+08:00" level=info msg="开始上传后端文件..."
