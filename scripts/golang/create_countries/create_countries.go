package main

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// Country 国家结构体，基于数据库模型
type Country struct {
	ID            uint64    `gorm:"primarykey" json:"id"`
	Name          string    `gorm:"type:varchar(100);not null" json:"name"`
	Code          string    `gorm:"type:varchar(10);uniqueIndex;not null" json:"code"`
	Flag          string    `gorm:"type:varchar(255)" json:"flag"`
	MerchantCount int64     `gorm:"type:bigint;default:0;not null" json:"merchant_count"`
	Status        int8      `gorm:"type:smallint;default:1;not null" json:"status"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 指定表名
func (Country) TableName() string {
	return "countries"
}

func main() {
	// 读取环境变量或使用默认值
	host := "**************"
	port := "14614"
	user := "user_SgXD8fYdcn7mPs6kttjk"
	password := "password_BkGnEmjRrBEJYcv8xHsU"
	dbname := "bonusearned"
	sslmode := "disable"
	timezone := "Asia/Shanghai"

	// 构建数据库连接字符串
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		host, port, user, password, dbname, sslmode, timezone,
	)

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("无法连接到数据库: %v", err)
	}

	// 获取一个SQL连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("无法获取数据库连接: %v", err)
	}
	defer sqlDB.Close()

	// 检查现有国家数据
	var existingCountries []Country
	if err := db.Find(&existingCountries).Error; err != nil {
		log.Fatalf("查询现有国家数据失败: %v", err)
	}

	log.Printf("数据库中现有 %d 个国家记录", len(existingCountries))

	// 定义要创建的国家数据（基于迁移文件中的数据）
	countries := []Country{
		{ID: 1, Name: "United States", Code: "US", Flag: "🇺🇸", Status: 1},
		{ID: 2, Name: "United Kingdom", Code: "UK", Flag: "🇬🇧", Status: 1},
		{ID: 3, Name: "Canada", Code: "CA", Flag: "🇨🇦", Status: 1},
		{ID: 4, Name: "Australia", Code: "AU", Flag: "🇦🇺", Status: 1},
		{ID: 5, Name: "Germany", Code: "DE", Flag: "🇩🇪", Status: 1},
		{ID: 6, Name: "France", Code: "FR", Flag: "🇫🇷", Status: 1},
		{ID: 7, Name: "Italy", Code: "IT", Flag: "🇮🇹", Status: 1},
		{ID: 8, Name: "Spain", Code: "ES", Flag: "🇪🇸", Status: 1},
		{ID: 9, Name: "Netherlands", Code: "NL", Flag: "🇳🇱", Status: 1},
		{ID: 10, Name: "Belgium", Code: "BE", Flag: "🇧🇪", Status: 1},
		{ID: 11, Name: "Switzerland", Code: "CH", Flag: "🇨🇭", Status: 1},
		{ID: 12, Name: "Austria", Code: "AT", Flag: "🇦🇹", Status: 1},
		{ID: 13, Name: "Sweden", Code: "SE", Flag: "🇸🇪", Status: 1},
		{ID: 14, Name: "Norway", Code: "NO", Flag: "🇳🇴", Status: 1},
		{ID: 15, Name: "Denmark", Code: "DK", Flag: "🇩🇰", Status: 1},
		{ID: 16, Name: "Finland", Code: "FI", Flag: "🇫🇮", Status: 1},
		{ID: 17, Name: "Poland", Code: "PL", Flag: "🇵🇱", Status: 1},
		{ID: 18, Name: "Czechia", Code: "CZ", Flag: "🇨🇿", Status: 1},
		{ID: 19, Name: "Hungary", Code: "HU", Flag: "🇭🇺", Status: 1},
		{ID: 20, Name: "Portugal", Code: "PT", Flag: "🇵🇹", Status: 1},
		{ID: 21, Name: "Ireland", Code: "IE", Flag: "🇮🇪", Status: 1},
		{ID: 22, Name: "Greece", Code: "GR", Flag: "🇬🇷", Status: 1},
		{ID: 23, Name: "Japan", Code: "JP", Flag: "🇯🇵", Status: 1},
		{ID: 24, Name: "South Korea", Code: "KR", Flag: "🇰🇷", Status: 1},
		{ID: 25, Name: "Singapore", Code: "SG", Flag: "🇸🇬", Status: 1},
		{ID: 26, Name: "Hong Kong", Code: "HK", Flag: "🇭🇰", Status: 1},
		{ID: 27, Name: "Taiwan", Code: "TW", Flag: "🇹🇼", Status: 1},
		{ID: 28, Name: "New Zealand", Code: "NZ", Flag: "🇳🇿", Status: 1},
		{ID: 29, Name: "Brazil", Code: "BR", Flag: "🇧🇷", Status: 1},
		{ID: 30, Name: "Mexico", Code: "MX", Flag: "🇲🇽", Status: 1},
		{ID: 31, Name: "India", Code: "IN", Flag: "🇮🇳", Status: 1},
		{ID: 32, Name: "China", Code: "CN", Flag: "🇨🇳", Status: 1},
		{ID: 33, Name: "Russia", Code: "RU", Flag: "🇷🇺", Status: 1},
		{ID: 34, Name: "South Africa", Code: "ZA", Flag: "🇿🇦", Status: 1},
		// 新增的国家（根据用户要求补充）
		{ID: 35, Name: "Chile", Code: "CL", Flag: "🇨🇱", Status: 1},
		{ID: 36, Name: "Argentina", Code: "AR", Flag: "🇦🇷", Status: 1},
		{ID: 37, Name: "Peru", Code: "PE", Flag: "🇵🇪", Status: 1},
		{ID: 38, Name: "Turkiye", Code: "TR", Flag: "🇹🇷", Status: 1},
		{ID: 39, Name: "Saudi Arabia", Code: "SA", Flag: "🇸🇦", Status: 1},
		{ID: 40, Name: "Malaysia", Code: "MY", Flag: "🇲🇾", Status: 1},
		{ID: 41, Name: "Romania", Code: "RO", Flag: "🇷🇴", Status: 1},
		{ID: 42, Name: "United Arab Emirates", Code: "AE", Flag: "🇦🇪", Status: 1},
	}

	log.Printf("准备创建 %d 个国家记录", len(countries))

	// 确认操作
	fmt.Printf("此操作将创建 %d 个国家记录到数据库中\n", len(countries))
	fmt.Print("请输入 'YES' 确认继续: ")
	var confirmation string
	fmt.Scanln(&confirmation)

	if confirmation != "YES" {
		log.Println("操作已取消")
		return
	}

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		log.Fatalf("开始事务失败: %v", tx.Error)
	}

	var createdCount int64 = 0
	var updatedCount int64 = 0

	// 批量处理国家数据
	for _, country := range countries {
		// 检查国家是否已存在
		var existingCountry Country
		result := tx.Where("code = ?", country.Code).First(&existingCountry)

		if result.Error == gorm.ErrRecordNotFound {
			// 国家不存在，创建新记录
			country.CreatedAt = time.Now()
			country.UpdatedAt = time.Now()

			if err := tx.Create(&country).Error; err != nil {
				tx.Rollback()
				log.Fatalf("创建国家记录失败 (Code: %s): %v", country.Code, err)
			}
			createdCount++
			log.Printf("创建国家: %s (%s)", country.Name, country.Code)
		} else if result.Error == nil {
			// 国家已存在，更新记录
			existingCountry.Name = country.Name
			existingCountry.Flag = country.Flag
			existingCountry.Status = country.Status
			existingCountry.UpdatedAt = time.Now()

			if err := tx.Save(&existingCountry).Error; err != nil {
				tx.Rollback()
				log.Fatalf("更新国家记录失败 (Code: %s): %v", country.Code, err)
			}
			updatedCount++
			log.Printf("更新国家: %s (%s)", country.Name, country.Code)
		} else {
			tx.Rollback()
			log.Fatalf("查询国家记录失败 (Code: %s): %v", country.Code, result.Error)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Fatalf("提交事务失败: %v", err)
	}

	log.Printf("操作完成！创建了 %d 个新国家，更新了 %d 个现有国家", createdCount, updatedCount)

	// 验证结果
	var finalCount int64
	db.Model(&Country{}).Count(&finalCount)
	log.Printf("数据库中现在共有 %d 个国家记录", finalCount)

	// 显示所有国家
	var allCountries []Country
	db.Order("id").Find(&allCountries)

	fmt.Println("\n所有国家列表:")
	for i, country := range allCountries {
		fmt.Printf("%3d. ID: %d, 代码: %s, 名称: %s, 国旗: %s, 状态: %d\n",
			i+1, country.ID, country.Code, country.Name, country.Flag, country.Status)
	}
}
