package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"bonusearned/config"
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/service"
	"bonusearned/infra/database"
	"bonusearned/infra/logger"
	"bonusearned/infra/persistence"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("local")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	db, err := database.NewPostgresDB(cfg.Postgres)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化Redis
	redisClient, err := database.NewRedisClient(cfg.Redis)
	if err != nil {
		log.Fatalf("Failed to connect to redis: %v", err)
	}

	// 初始化日志
	zapLogger, err := logger.NewLogger(cfg.Logger)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	// 创建仓储和服务
	cashbackRuleRepo := persistence.NewCashbackRulePostgresRepository(db)
	cashbackRuleService := service.NewCashbackRuleService(cashbackRuleRepo, zapLogger, redisClient)

	// 创建Gin上下文
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	req, _ := http.NewRequest("GET", "/", nil)
	ctx.Request = req.WithContext(context.Background())

	fmt.Println("=== 简单CashbackRule测试 ===")

	// 测试用户ID
	testUserID := uint64(1)

	// 1. 测试创建全局返现规则
	fmt.Println("\n1. 创建全局返现规则（85%）")
	globalRule := &entity.CashbackRule{
		UserID:       testUserID,
		CashbackRate: decimal.NewFromFloat(0.85),
		IsGlobal:     true,
		Status:       1,
		StartTime:    time.Now(),
		EndTime:      time.Now().Add(30 * 24 * time.Hour),
	}

	err = cashbackRuleService.CreateCashbackRule(ctx, globalRule)
	if err != nil {
		fmt.Printf("❌ 失败: %v\n", err)
	} else {
		fmt.Printf("✅ 成功: 85%% 全局规则\n")
	}

	// 2. 测试创建特定商家返现规则
	fmt.Println("\n2. 创建特定商家返现规则（90%）")
	merchantRule := &entity.CashbackRule{
		UserID:       testUserID,
		MerchantID:   1,
		CashbackRate: decimal.NewFromFloat(0.90),
		IsGlobal:     false,
		Status:       1,
		StartTime:    time.Now(),
		EndTime:      time.Now().Add(30 * 24 * time.Hour),
	}

	err = cashbackRuleService.CreateCashbackRule(ctx, merchantRule)
	if err != nil {
		fmt.Printf("❌ 失败: %v\n", err)
	} else {
		fmt.Printf("✅ 成功: 90%% 商家规则 (MerchantID=1)\n")
	}

	// 3. 测试重复创建（应该失败）
	fmt.Println("\n3. 测试重复创建全局规则（应该失败）")
	duplicateRule := &entity.CashbackRule{
		UserID:       testUserID,
		CashbackRate: decimal.NewFromFloat(0.75),
		IsGlobal:     true,
		Status:       1,
	}

	err = cashbackRuleService.CreateCashbackRule(ctx, duplicateRule)
	if err != nil {
		fmt.Printf("✅ 正确阻止重复创建: %v\n", err)
	} else {
		fmt.Printf("❌ 应该阻止重复创建但没有阻止\n")
	}

	// 4. 测试获取规则
	fmt.Println("\n4. 获取全局规则")
	globalRuleResult, err := cashbackRuleService.GetCashbackGlobalRuleByUserId(ctx, testUserID)
	if err != nil {
		fmt.Printf("❌ 获取失败: %v\n", err)
	} else if globalRuleResult != nil {
		fmt.Printf("✅ 获取成功: Rate=%.2f%%, IsGlobal=%v\n",
			globalRuleResult.CashbackRate.InexactFloat64()*100, globalRuleResult.IsGlobal)
	} else {
		fmt.Printf("❌ 获取失败: 返回数据为空\n")
	}

	// 5. 测试获取特定商家规则
	fmt.Println("\n5. 获取特定商家规则")
	merchantRuleResult, err := cashbackRuleService.GetCashbackRuleByUserAndMerchant(ctx, testUserID, 1)
	if err != nil {
		fmt.Printf("❌ 获取失败: %v\n", err)
	} else if merchantRuleResult != nil {
		fmt.Printf("✅ 获取成功: Rate=%.2f%%, MerchantID=%d\n",
			merchantRuleResult.CashbackRate.InexactFloat64()*100, merchantRuleResult.MerchantID)
	} else {
		fmt.Printf("❌ 获取失败: 返回数据为空\n")
	}

	// 6. 测试应用返现规则到虚拟商家
	fmt.Println("\n6. 测试应用返现规则")
	testMerchant := &entity.Merchant{
		ID:            1,
		Name:          "Test Merchant",
		CashbackRate:  decimal.NewFromFloat(0.80), // 原始80%
		CashbackValue: decimal.NewFromFloat(5.00), // 原始$5
	}

	fmt.Printf("原始商家: Rate=%.2f%%, Value=$%.2f\n",
		testMerchant.CashbackRate.InexactFloat64()*100, testMerchant.CashbackValue.InexactFloat64())

	updatedMerchant, err := cashbackRuleService.ApplyCashbackRate(ctx, testUserID, testMerchant)
	if err != nil {
		fmt.Printf("❌ 应用失败: %v\n", err)
	} else if updatedMerchant != nil {
		fmt.Printf("✅ 应用成功: Rate=%.2f%%, Value=$%.2f\n",
			updatedMerchant.CashbackRate.InexactFloat64()*100, updatedMerchant.CashbackValue.InexactFloat64())
	} else {
		fmt.Printf("❌ 应用失败: 返回数据为空\n")
	}

	fmt.Println("\n=== 测试完成 ===")
}
