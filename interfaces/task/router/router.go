package router

import (
	"bonusearned/application/task/appservice"
	"bonusearned/config"
	clickrecordservice "bonusearned/domain/clickrecord/service"
	couponservice "bonusearned/domain/coupon/service"
	"bonusearned/domain/merchant/service"
	orderservice "bonusearned/domain/order/service"
	taskService "bonusearned/domain/task/service"
	userservice "bonusearned/domain/user/service"
	"bonusearned/infra/persistence"
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Server 任务服务器
type Server struct {
	syncApp appservice.SyncApp
	logger  *zap.Logger
	router  *gin.Engine
	cron    *cron.Cron
	server  *http.Server
}

// NewServer 创建任务服务器
func NewServer(cfg *config.Config, db *gorm.DB, redisClient *redis.Client, logger *zap.Logger) *Server {
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()
	// 使用 Recovery 中间件
	router.Use(gin.Recovery())
	// 初始化各层服务

	cashbackRuleRepo := persistence.NewCashbackRulePostgresRepository(db)
	cashbackRuleService := service.NewCashbackRuleService(
		cashbackRuleRepo,
		logger,
		redisClient,
	)

	orderRepo := persistence.NewOrderPostgresRepository(db)
	orderService := orderservice.NewOrderService(
		orderRepo,
		redisClient,
		logger,
	)

	clickRecordRepo := persistence.NewClickPostgresRepository(db)
	clickRecordService := clickrecordservice.NewClickRecordService(
		clickRecordRepo,
		logger,
	)

	userRepo := persistence.NewUserPostgresRepository(db)
	userService := userservice.NewUserService(
		userRepo,
		redisClient,
		cfg,
		logger,
	)

	merchantRepo := persistence.NewMerchantPostgresRepository(db)
	merchantService := service.NewMerchantService(
		merchantRepo,
		cashbackRuleService,
		redisClient,
		cfg,
		logger,
	)

	countryRepo := persistence.NewCountryPostgresRepository(db)
	countryService := service.NewCountryService(
		countryRepo,
		logger,
		redisClient,
	)

	couponRepo := persistence.NewCouponPostgresRepository(db)
	couponService := couponservice.NewCouponService(
		couponRepo,
	)

	taskDomainService := taskService.NewSyncService(merchantService, countryService, merchantRepo, orderService, clickRecordService, couponService, userService, cfg)
	taskAppService := appservice.NewSyncApp(taskDomainService)

	s := &Server{
		syncApp: taskAppService,
		router:  router,
		logger:  logger,
		cron:    cron.New(),
	}

	s.setupRoutes()
	s.setupCronJobs()
	return s
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 设置API路由
	api := s.router.Group("/api")
	{
		// 手动触发同步任务
		api.POST("/sync/merchants", func(c *gin.Context) {
			go s.syncApp.SyncMerchantList(c)
			c.JSON(http.StatusOK, gin.H{"message": "Merchant sync started"})
		})

		api.POST("/sync/coupons", func(c *gin.Context) {
			go s.syncApp.SyncCouponList(c)
			c.JSON(http.StatusOK, gin.H{"message": "Coupon sync started"})
		})

		api.POST("/sync/orders", func(c *gin.Context) {
			go s.syncApp.SyncOrderList(c)
			c.JSON(http.StatusOK, gin.H{"message": "Order sync started"})
		})

		api.POST("/sync/merchant-logos", func(c *gin.Context) {
			go s.syncApp.SyncMerchantLogoList(c)
			c.JSON(http.StatusOK, gin.H{"message": "Merchant logo sync started"})
		})
	}
}

// setupCronJobs 设置定时任务
func (s *Server) setupCronJobs() {
	// 每天凌晨2点同步商家信息
	s.cron.AddFunc("47 22 * * *", func() {
		go s.syncApp.SyncMerchantList(&gin.Context{})
	})

	s.cron.AddFunc("10 04 * * *", func() {
		go s.syncApp.SyncCouponList(&gin.Context{})
	})

	s.cron.AddFunc("26 * * * *", func() {
		go s.syncApp.SyncOrderList(&gin.Context{})
	})

	// 每周一凌晨3点同步商家Logo
	s.cron.AddFunc("02 12 * * *", func() {
		go s.syncApp.SyncMerchantLogoList(&gin.Context{})
	})

	// 启动定时任务
	s.cron.Start()
}

// Run 运行服务器
func (s *Server) Run(addr string) error {
	srv := &http.Server{
		Addr:         addr,
		Handler:      s.router,
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 10 * time.Second,
	}

	s.logger.Info("Task server is running at " + addr)
	return srv.ListenAndServe()
}

// Stop 停止服务器
func (s *Server) Stop() {
	s.cron.Stop()
}

// Shutdown 优雅关闭服务器
func (s *Server) Shutdown(ctx context.Context) error {
	if s.server != nil {
		return s.server.Shutdown(ctx)
	}
	return nil
}
