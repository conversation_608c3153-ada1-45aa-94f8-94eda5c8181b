package persistence

import (
	"bonusearned/domain/order/entity"
	"bonusearned/domain/order/repository"
	"bonusearned/infra/ecode"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type orderPostgresRepository struct {
	db *gorm.DB
}

// NewOrderPostgresRepository 创建订单仓储
func NewOrderPostgresRepository(db *gorm.DB) repository.OrderRepository {
	return &orderPostgresRepository{db: db}
}

// GetOrderDetailById 根据ID获取订单
func (r *orderPostgresRepository) GetOrderDetailById(ctx *gin.Context, id uint64) (*entity.Order, *ecode.Error) {
	var order entity.Order
	if err := r.db.WithContext(ctx).First(&order, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &order, nil
}

// GetOrderListByCondition 获取订单列表
func (r *orderPostgresRepository) GetOrderListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Order, int64, *ecode.Error) {
	var orders []*entity.Order
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Order{})

	// 处理搜索条件
	if userID, ok := condition["user_id"].(uint64); ok && userID > 0 {
		query = query.Where("user_id = ?", userID)
	}

	if merchantID, ok := condition["merchant_id"].(uint64); ok && merchantID > 0 {
		query = query.Where("merchant_id = ?", merchantID)
	}

	if status, ok := condition["status"].(int8); ok {
		query = query.Where("status = ?", status)
	}

	query = query.Order("created_at ASC")

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&orders).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return orders, total, nil
}

// CreateOrder 创建订单
func (r *orderPostgresRepository) CreateOrder(ctx *gin.Context, order *entity.Order) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(order).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateOrder 更新订单
func (r *orderPostgresRepository) UpdateOrder(ctx *gin.Context, order *entity.Order) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(order).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateOrderStatus 更新订单状态
func (r *orderPostgresRepository) UpdateOrderStatus(ctx *gin.Context, id uint64, status entity.OrderStatus) *ecode.Error {
	if err := r.db.WithContext(ctx).Model(&entity.Order{}).Where("id = ?", id).Update("status", status).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// DeleteOrder 删除订单
func (r *orderPostgresRepository) DeleteOrder(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.Order{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// BatchCreateOrder 批量创建订单
func (r *orderPostgresRepository) BatchCreateOrder(ctx *gin.Context, orders []*entity.Order) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(&orders).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// BatchUpdateOrder 批量更新订单
func (r *orderPostgresRepository) BatchUpdateOrder(ctx *gin.Context, orders []*entity.Order) *ecode.Error {
	if err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, order := range orders {
			if err := tx.Save(order).Error; err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// BatchUpdateOrderStatus 批量更新订单状态
func (r *orderPostgresRepository) BatchUpdateOrderStatus(ctx *gin.Context, ids []uint64, status entity.OrderStatus) *ecode.Error {
	if err := r.db.WithContext(ctx).Model(&entity.Order{}).Where("id IN ?", ids).Update("status", status).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}
