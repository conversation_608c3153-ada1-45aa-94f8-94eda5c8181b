package persistence

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/domain/merchant/repository"
	"bonusearned/infra/ecode"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type cashbackRulePostgresRepository struct {
	db *gorm.DB
}

// NewCashbackRulePostgresRepository 创建商家返现规则仓储
func NewCashbackRulePostgresRepository(db *gorm.DB) repository.CashbackRuleRepository {
	return &cashbackRulePostgresRepository{db: db}
}

// CreateCashbackRule 创建商家返现规则
func (r *cashbackRulePostgresRepository) CreateCashbackRule(ctx *gin.Context, rule *entity.CashbackRule) *ecode.Error {
	if err := r.db.WithContext(ctx).Create(rule).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// UpdateCashbackRule 更新商家返现规则
func (r *cashbackRulePostgresRepository) UpdateCashbackRule(ctx *gin.Context, rule *entity.CashbackRule) *ecode.Error {
	if err := r.db.WithContext(ctx).Save(rule).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}

// GetCashbackRuleDetailByID 根据ID获取商家返现规则
func (r *cashbackRulePostgresRepository) GetCashbackRuleDetailByID(ctx *gin.Context, id uint64) (*entity.CashbackRule, *ecode.Error) {
	var rule entity.CashbackRule
	if err := r.db.WithContext(ctx).First(&rule, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.ErrDatabase
	}
	return &rule, nil
}

// GetCashbackRuleByUserAndMerchant 根据用户ID和商家ID获取商家返现规则
func (r *cashbackRulePostgresRepository) GetCashbackRuleByUserAndMerchant(ctx *gin.Context, userID, merchantID uint64) (*entity.CashbackRule, *ecode.Error) {
	var rule entity.CashbackRule
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND merchant_id = ? AND is_global = ? AND status = ?",
			userID, merchantID, false, 1).
		First(&rule).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get cashback rule by user and merchant")
	}
	return &rule, nil
}

// GetCashbackGlobalRuleByUserId 获取用户的全局返现规则
func (r *cashbackRulePostgresRepository) GetCashbackGlobalRuleByUserId(ctx *gin.Context, userID uint64) (*entity.CashbackRule, *ecode.Error) {
	var rule entity.CashbackRule
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND is_global = ? AND merchant_id = ? AND status = ?",
			userID, true, 0, 1).
		First(&rule).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get global cashback rule")
	}
	return &rule, nil
}

// GetCashbackRuleList 获取所有返现规则
func (r *cashbackRulePostgresRepository) GetCashbackRuleList(ctx *gin.Context, condition map[string]interface{}) ([]*entity.CashbackRule, int64, *ecode.Error) {
	var rules []*entity.CashbackRule
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.CashbackRule{})

	// 处理搜索条件
	if userID, ok := condition["user_id"].(uint64); ok && userID > 0 {
		query = query.Where("user_id = ?", userID)
	}

	if merchantID, ok := condition["merchant_id"].(uint64); ok && merchantID > 0 {
		query = query.Where("merchant_id = ?", merchantID)
	}

	if isGlobal, ok := condition["is_global"].(bool); ok {
		query = query.Where("is_global = ?", isGlobal)
	}

	if status, ok := condition["status"].(int8); ok {
		query = query.Where("status = ?", status)
	}

	query = query.Order("created_at DESC")
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	// 处理分页
	if offset, ok := condition["offset"].(int); ok {
		query = query.Offset(offset)
	}
	if limit, ok := condition["page_size"].(int); ok {
		query = query.Limit(limit)
	}

	// 获取列表
	if err := query.Find(&rules).Error; err != nil {
		return nil, 0, ecode.ErrDatabase
	}

	return rules, total, nil
}

// DeleteCashbackRule 删除返现规则
func (r *cashbackRulePostgresRepository) DeleteCashbackRule(ctx *gin.Context, id uint64) *ecode.Error {
	if err := r.db.WithContext(ctx).Delete(&entity.CashbackRule{}, id).Error; err != nil {
		return ecode.ErrDatabase
	}
	return nil
}
