package cachekey

import "time"

// 缓存key前缀常量
const (
	PrefixUser         = "user"
	PrefixMerchant     = "merchant"
	PrefixCategory     = "category"
	PrefixCoupon       = "coupon"
	PrefixBlog         = "blog"
	PrefixCashbackRule = "cashback_rule"
	PrefixOrder        = "order"
	PrefixCountry      = "country"

	// 过期时间相关常量
	DefaultExpiration = time.Hour       // 1小时
	LongExpiration    = 24 * time.Hour  // 24小时
	ShortExpiration   = 5 * time.Minute // 5分钟
)
