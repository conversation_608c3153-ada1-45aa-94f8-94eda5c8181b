package cachekey

import (
	"bonusearned/infra/constant"
	"crypto/md5"
	"fmt"
	"sort"
	"strings"
)

// GenerateUserByIdKey 生成用户相关的缓存key
func GenerateUserByIdKey(id uint64) string {
	return fmt.Sprintf("%s:id:%d", PrefixUser, id)
}

func GenerateUserByEmailKey(email string) string {
	return fmt.Sprintf("%s:email:%s", PrefixUser, email)
}
func GenerateUserByCodeKey(userCode string) string {
	return fmt.Sprintf("%s:code:%s", PrefixUser, userCode)
}

// GenerateMerchantIdKey 生成商家相关的缓存key
func GenerateMerchantIdKey(id uint64) string {
	return fmt.Sprintf("%s:id:%d", PrefixMerchant, id)
}

func GenerateMerchantByIdKey(id uint64) string {
	return fmt.Sprintf("%s:%d", PrefixMerchant, id)
}

func GenerateMerchantByCodeKey(code string) string {
	return fmt.Sprintf("%s:code:%s", PrefixMerchant, code)
}

func GenerateMerchantTrackByCodeKey(code string) string {
	return fmt.Sprintf("%s:code:track:%s", PrefixMerchant, code)
}

func GenerateMerchantByCodeKeyNew(code string) string {
	return fmt.Sprintf("%s:code:%s", PrefixMerchant, code)
}

func GenerateMerchantByUniqueNameKey(uniqueName string) string {
	return fmt.Sprintf("%s:unique_name:%s", PrefixMerchant, uniqueName)
}

func GenerateMerchantByUniqueNameKeyNew(uniqueName string) string {
	return fmt.Sprintf("%s:unique_name:%s", PrefixMerchant, uniqueName)
}

func GenerateMerchantListKey(condition map[string]interface{}) string {
	if len(condition) == 0 {
		return fmt.Sprintf("%s:list:all", PrefixMerchant)
	}

	// 对条件进行排序，确保相同的条件生成相同的key
	keys := make([]string, 0, len(condition))
	for k := range condition {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建条件字符串
	var parts []string
	for _, k := range keys {
		v := condition[k]
		// 对不同类型的值进行处理
		var valueStr string
		switch val := v.(type) {
		case string:
			valueStr = val
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			valueStr = fmt.Sprintf("%d", val)
		case float32, float64:
			valueStr = fmt.Sprintf("%.2f", val)
		case bool:
			valueStr = fmt.Sprintf("%v", val)
		case []interface{}:
			// 对数组类型的处理
			items := make([]string, len(val))
			for i, item := range val {
				items[i] = fmt.Sprintf("%v", item)
			}
			sort.Strings(items) // 排序确保顺序一致
			valueStr = strings.Join(items, constant.SEPARATOR)
		default:
			valueStr = fmt.Sprintf("%v", val)
		}
		parts = append(parts, fmt.Sprintf("%s%s%s", k, constant.SEPARATOR, valueStr))
	}

	//// 使用MD5对长条件进行哈希，避免key过长
	//if len(parts) > 3 {
	//	condStr := strings.Join(parts, constant.SEPARATOR)
	//	hash := md5.Sum([]byte(condStr))
	//	return fmt.Sprintf("%s:list:%x", PrefixMerchant, hash)
	//}

	return fmt.Sprintf("%s:list:%s", PrefixMerchant, strings.Join(parts, constant.SEPARATOR))
}

func GenerateMerchantListKeyNew(condition map[string]interface{}) string {
	if condition == nil {
		return fmt.Sprintf("%s:list:all", PrefixMerchant)
	}

	// 将条件按键排序，确保相同的条件生成相同的key
	var keys []string
	for k := range condition {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var parts []string
	for _, k := range keys {
		v := condition[k]
		parts = append(parts, fmt.Sprintf("%s=%v", k, v))
	}

	return fmt.Sprintf("%s:list:%s", PrefixMerchant, strings.Join(parts, ":"))
}

// GenerateCouponKey 生成优惠券相关的缓存key
func GenerateCouponKey(id uint64) string {
	return fmt.Sprintf("%s%sid%s%d", PrefixCoupon, constant.SEPARATOR, constant.SEPARATOR, id)
}

func GenerateCouponByCodeKey(code string) string {
	return fmt.Sprintf("%s%scodes%s%s", PrefixCoupon, constant.SEPARATOR, constant.SEPARATOR, code)
}

func GenerateCouponsByMerchantKey(merchantID uint64) string {
	return fmt.Sprintf("%s%smerchant%s%d%slist", PrefixCoupon, constant.SEPARATOR, constant.SEPARATOR, merchantID, constant.SEPARATOR)
}

// GenerateBlogKey 生成博客相关的缓存key
func GenerateBlogKey(id uint64) string {
	return fmt.Sprintf("%s%sid%s%d", PrefixBlog, constant.SEPARATOR, constant.SEPARATOR, id)
}

func GenerateBlogBySlugKey(slug string) string {
	return fmt.Sprintf("%s%sslug%s%s", PrefixBlog, constant.SEPARATOR, constant.SEPARATOR, slug)
}

func GenerateBlogLatestListKey() string {
	return fmt.Sprintf("%s%slatest_list", PrefixBlog, constant.SEPARATOR)
}

// GenerateCashbackRuleKey 生成返现规则相关的缓存key
func GenerateCashbackRuleKey(id uint64) string {
	return fmt.Sprintf("%s:id:%d", PrefixCashbackRule, id)
}

func GenerateCashbackRuleByUserKey(userID uint64) string {
	return fmt.Sprintf("%s:user:%d:global", PrefixCashbackRule, userID)
}

func GenerateCashbackRuleByUserAndMerchantKey(userID, merchantID uint64) string {
	return fmt.Sprintf("%s:user:%d:merchant:%d", PrefixCashbackRule, userID, merchantID)
}

// GenerateOrderKey 生成订单相关的缓存key
func GenerateOrderKey(id uint64) string {
	return fmt.Sprintf("%s%sid%s%d", PrefixOrder, constant.SEPARATOR, constant.SEPARATOR, id)
}

// GenerateCategoryIdKey 生成分类相关的缓存key
func GenerateCategoryIdKey(id uint64) string {
	return fmt.Sprintf("%s:id:%d", PrefixCategory, id)
}

// GenerateCategoryNameKey 生成分类相关的缓存key
func GenerateCategoryNameKey(name string) string {
	return fmt.Sprintf("%s:name:%s", PrefixCategory, name)
}

// GenerateCategoryListKey 生成分类相关的缓存key
func GenerateCategoryListKey(condition map[string]interface{}) string {
	if len(condition) == 0 {
		return fmt.Sprintf("%s:list:all", PrefixCategory)
	}

	// 对条件进行排序，确保相同的条件生成相同的key
	keys := make([]string, 0, len(condition))
	for k := range condition {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建条件字符串
	var parts []string
	for _, k := range keys {
		v := condition[k]
		// 对不同类型的值进行处理
		var valueStr string
		switch val := v.(type) {
		case string:
			valueStr = val
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			valueStr = fmt.Sprintf("%d", val)
		case float32, float64:
			valueStr = fmt.Sprintf("%.2f", val)
		case bool:
			valueStr = fmt.Sprintf("%v", val)
		case []interface{}:
			// 对数组类型的处理
			items := make([]string, len(val))
			for i, item := range val {
				items[i] = fmt.Sprintf("%v", item)
			}
			sort.Strings(items) // 排序确保顺序一致
			valueStr = strings.Join(items, constant.SEPARATOR)
		default:
			valueStr = fmt.Sprintf("%v", val)
		}
		parts = append(parts, fmt.Sprintf("%s%s%s", k, constant.SEPARATOR, valueStr))
	}

	// 使用MD5对长条件进行哈希，避免key过长
	if len(parts) > 3 {
		condStr := strings.Join(parts, constant.SEPARATOR)
		hash := md5.Sum([]byte(condStr))
		return fmt.Sprintf("%s:list:%x", PrefixCategory, hash)
	}

	return fmt.Sprintf("%s:list:%s", PrefixCategory, strings.Join(parts, constant.SEPARATOR))
}

// GenerateCashbackRuleListKey 生成返利规则相关的缓存key
func GenerateCashbackRuleListKey(condition map[string]interface{}) string {
	if len(condition) == 0 {
		return fmt.Sprintf("%s:list:all", PrefixCashbackRule)
	}

	// 对条件进行排序，确保相同的条件生成相同的key
	keys := make([]string, 0, len(condition))
	for k := range condition {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建条件字符串
	var parts []string
	for _, k := range keys {
		v := condition[k]
		// 对不同类型的值进行处理
		var valueStr string
		switch val := v.(type) {
		case string:
			valueStr = val
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			valueStr = fmt.Sprintf("%d", val)
		case float32, float64:
			valueStr = fmt.Sprintf("%.2f", val)
		case bool:
			valueStr = fmt.Sprintf("%v", val)
		case []interface{}:
			// 对数组类型的处理
			items := make([]string, len(val))
			for i, item := range val {
				items[i] = fmt.Sprintf("%v", item)
			}
			sort.Strings(items) // 排序确保顺序一致
			valueStr = strings.Join(items, constant.SEPARATOR)
		default:
			valueStr = fmt.Sprintf("%v", val)
		}
		parts = append(parts, fmt.Sprintf("%s%s%s", k, constant.SEPARATOR, valueStr))
	}

	// 使用MD5对长条件进行哈希，避免key过长
	if len(parts) > 3 {
		condStr := strings.Join(parts, constant.SEPARATOR)
		hash := md5.Sum([]byte(condStr))
		return fmt.Sprintf("%s:list:%x", PrefixCashbackRule, hash)
	}

	return fmt.Sprintf("%s:list:%s", PrefixCashbackRule, strings.Join(parts, constant.SEPARATOR))
}

// GenerateCountryIdKey 生成国家相关的缓存key
func GenerateCountryIdKey(id uint64) string {
	return fmt.Sprintf("%s:id:%d", PrefixCountry, id)
}

// GenerateCountryCodeKey 生成国家代码相关的缓存key
func GenerateCountryCodeKey(code string) string {
	return fmt.Sprintf("%s:code:%s", PrefixCountry, code)
}

// GenerateCountryListKey 生成国家列表相关的缓存key
func GenerateCountryListKey(condition map[string]interface{}) string {
	if len(condition) == 0 {
		return fmt.Sprintf("%s:list:all", PrefixCountry)
	}

	// 对条件进行排序，确保相同的条件生成相同的key
	keys := make([]string, 0, len(condition))
	for k := range condition {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建条件字符串
	var parts []string
	for _, k := range keys {
		v := condition[k]
		// 对不同类型的值进行处理
		var valueStr string
		switch val := v.(type) {
		case string:
			valueStr = val
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			valueStr = fmt.Sprintf("%d", val)
		case float32, float64:
			valueStr = fmt.Sprintf("%.2f", val)
		case bool:
			valueStr = fmt.Sprintf("%v", val)
		case []interface{}:
			// 对数组类型的处理
			items := make([]string, len(val))
			for i, item := range val {
				items[i] = fmt.Sprintf("%v", item)
			}
			sort.Strings(items) // 排序确保顺序一致
			valueStr = strings.Join(items, constant.SEPARATOR)
		default:
			valueStr = fmt.Sprintf("%v", val)
		}
		parts = append(parts, fmt.Sprintf("%s%s%s", k, constant.SEPARATOR, valueStr))
	}

	// 使用MD5对长条件进行哈希，避免key过长
	if len(parts) > 3 {
		condStr := strings.Join(parts, constant.SEPARATOR)
		hash := md5.Sum([]byte(condStr))
		return fmt.Sprintf("%s:list:%x", PrefixCountry, hash)
	}

	return fmt.Sprintf("%s:list:%s", PrefixCountry, strings.Join(parts, constant.SEPARATOR))
}

func GenerateUserListKey(condition map[string]interface{}) string {
	if len(condition) == 0 {
		return fmt.Sprintf("%s:list:all", PrefixUser)
	}

	// 对条件进行排序，确保相同的条件生成相同的key
	keys := make([]string, 0, len(condition))
	for k := range condition {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建条件字符串
	var parts []string
	for _, k := range keys {
		v := condition[k]
		// 对不同类型的值进行处理
		var valueStr string
		switch val := v.(type) {
		case string:
			valueStr = val
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			valueStr = fmt.Sprintf("%d", val)
		case float32, float64:
			valueStr = fmt.Sprintf("%.2f", val)
		case bool:
			valueStr = fmt.Sprintf("%v", val)
		case []interface{}:
			// 对数组类型的处理
			items := make([]string, len(val))
			for i, item := range val {
				items[i] = fmt.Sprintf("%v", item)
			}
			sort.Strings(items) // 排序确保顺序一致
			valueStr = strings.Join(items, constant.SEPARATOR)
		default:
			valueStr = fmt.Sprintf("%v", val)
		}
		parts = append(parts, fmt.Sprintf("%s%s%s", k, constant.SEPARATOR, valueStr))
	}

	//// 使用MD5对长条件进行哈希，避免key过长
	//if len(parts) > 3 {
	//	condStr := strings.Join(parts, constant.SEPARATOR)
	//	hash := md5.Sum([]byte(condStr))
	//	return fmt.Sprintf("%s:list:%x", PrefixMerchant, hash)
	//}

	return fmt.Sprintf("%s:list:%s", PrefixUser, strings.Join(parts, constant.SEPARATOR))
}
